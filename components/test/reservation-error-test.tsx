import React from "react";
import { Button, ButtonText } from "@/components/ui/button";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { useReserveClass } from "@/data/screens/classes/mutations/useReserveClass";

export const ReservationErrorTest = () => {
  const { mutate: reserveClass, isPending, error } = useReserveClass();

  const testReservationError = () => {
    // This should trigger an error since we're passing invalid data
    reserveClass({
      class_id: -1, // Invalid class ID
      date: "invalid-date", // Invalid date format
      is_virtual: false,
    });
  };

  const testReservationWithValidData = () => {
    // This might also fail if the class doesn't exist, which is what we want for testing
    reserveClass({
      class_id: 999999, // Non-existent class ID
      date: "2024-12-31", // Valid date format but likely no class
      is_virtual: false,
    });
  };

  return (
    <VStack space="md" className="p-4 border border-gray-300 rounded-lg">
      <Text className="text-lg font-dm-sans-bold">Reservation Error Test</Text>
      
      <Text className="text-sm text-gray-600">
        These buttons will trigger reservation errors to test the alert system:
      </Text>
      
      <Button 
        onPress={testReservationError} 
        className="bg-red-500"
        disabled={isPending}
      >
        <ButtonText>
          {isPending ? "Testing..." : "Test Invalid Data Error"}
        </ButtonText>
      </Button>
      
      <Button 
        onPress={testReservationWithValidData} 
        className="bg-orange-500"
        disabled={isPending}
      >
        <ButtonText>
          {isPending ? "Testing..." : "Test Non-existent Class Error"}
        </ButtonText>
      </Button>
      
      {error && (
        <Text className="text-red-500 text-sm">
          Last error: {error.message}
        </Text>
      )}
    </VStack>
  );
};
