import React, { useState } from "react";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Button, ButtonText } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { useSession } from "@/modules/login/auth-provider";
import { getBiometricCredentials, isBiometricEnabled } from "@/modules/login/biometric-storage";
import { Fingerprint, CheckCircle, XCircle, AlertCircle } from "lucide-react-native";

export const BiometricTest = () => {
  const { 
    biometricState, 
    enableBiometric, 
    disableBiometric, 
    checkBiometricStatus,
    signInWithBiometric 
  } = useSession();
  
  const [testResults, setTestResults] = useState<{
    credentialsExist: boolean | null;
    enabledStatus: boolean | null;
    authenticationTest: boolean | null;
    lastError: string | null;
  }>({
    credentialsExist: null,
    enabledStatus: null,
    authenticationTest: null,
    lastError: null,
  });

  const [isLoading, setIsLoading] = useState(false);

  const runDiagnostics = async () => {
    setIsLoading(true);
    setTestResults({
      credentialsExist: null,
      enabledStatus: null,
      authenticationTest: null,
      lastError: null,
    });

    try {
      // Test 1: Check if credentials exist
      const credentials = await getBiometricCredentials();
      const credentialsExist = !!credentials;

      // Test 2: Check enabled status
      const enabledStatus = await isBiometricEnabled();

      // Test 3: Refresh biometric status
      await checkBiometricStatus();

      setTestResults({
        credentialsExist,
        enabledStatus,
        authenticationTest: null,
        lastError: null,
      });

      console.log("Biometric diagnostics:", {
        credentialsExist,
        enabledStatus,
        biometricState,
      });
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        lastError: error instanceof Error ? error.message : "Unknown error",
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const testBiometricAuth = async () => {
    setIsLoading(true);
    try {
      const success = await signInWithBiometric();
      setTestResults(prev => ({
        ...prev,
        authenticationTest: success,
        lastError: success ? null : "Authentication failed",
      }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        authenticationTest: false,
        lastError: error instanceof Error ? error.message : "Unknown error",
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const testEnableBiometric = async () => {
    setIsLoading(true);
    try {
      // Use dummy credentials for testing
      const success = await enableBiometric("<EMAIL>", "testpassword");
      setTestResults(prev => ({
        ...prev,
        lastError: success ? null : "Failed to enable biometric",
      }));
      
      if (success) {
        // Re-run diagnostics to update state
        await runDiagnostics();
      }
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        lastError: error instanceof Error ? error.message : "Unknown error",
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const StatusIcon = ({ status }: { status: boolean | null }) => {
    if (status === null) return <AlertCircle className="text-gray-400" size={16} />;
    return status ? 
      <CheckCircle className="text-green-500" size={16} /> : 
      <XCircle className="text-red-500" size={16} />;
  };

  return (
    <VStack space="md" className="p-4 border border-gray-300 rounded-lg">
      <HStack className="items-center" space="sm">
        <Fingerprint className="text-[#00697B]" size={24} />
        <Text className="text-lg font-dm-sans-bold">Biometric Authentication Test</Text>
      </HStack>
      
      <Text className="text-sm text-gray-600">
        Use this panel to test and diagnose biometric authentication issues.
      </Text>

      {/* Current State Display */}
      <VStack space="sm" className="bg-gray-50 p-3 rounded-lg">
        <Text className="font-dm-sans-medium">Current State:</Text>
        <HStack className="justify-between">
          <Text className="text-sm">Supported:</Text>
          <StatusIcon status={biometricState.isSupported} />
        </HStack>
        <HStack className="justify-between">
          <Text className="text-sm">Enrolled:</Text>
          <StatusIcon status={biometricState.isEnrolled} />
        </HStack>
        <HStack className="justify-between">
          <Text className="text-sm">Enabled:</Text>
          <StatusIcon status={biometricState.isEnabled} />
        </HStack>
        <HStack className="justify-between">
          <Text className="text-sm">Loading:</Text>
          <StatusIcon status={biometricState.isLoading} />
        </HStack>
      </VStack>

      {/* Test Results */}
      <VStack space="sm" className="bg-blue-50 p-3 rounded-lg">
        <Text className="font-dm-sans-medium">Test Results:</Text>
        <HStack className="justify-between">
          <Text className="text-sm">Credentials Exist:</Text>
          <StatusIcon status={testResults.credentialsExist} />
        </HStack>
        <HStack className="justify-between">
          <Text className="text-sm">Storage Enabled:</Text>
          <StatusIcon status={testResults.enabledStatus} />
        </HStack>
        <HStack className="justify-between">
          <Text className="text-sm">Auth Test:</Text>
          <StatusIcon status={testResults.authenticationTest} />
        </HStack>
      </VStack>

      {/* Error Display */}
      {testResults.lastError && (
        <VStack className="bg-red-50 p-3 rounded-lg">
          <Text className="text-red-800 font-dm-sans-medium">Last Error:</Text>
          <Text className="text-red-600 text-sm">{testResults.lastError}</Text>
        </VStack>
      )}

      {/* Test Buttons */}
      <VStack space="sm">
        <Button 
          onPress={runDiagnostics} 
          disabled={isLoading}
          className="bg-blue-500"
        >
          <ButtonText>
            {isLoading ? "Running..." : "Run Diagnostics"}
          </ButtonText>
        </Button>

        <Button 
          onPress={testEnableBiometric} 
          disabled={isLoading || !biometricState.isSupported}
          className="bg-green-500"
        >
          <ButtonText>
            {isLoading ? "Enabling..." : "Test Enable Biometric"}
          </ButtonText>
        </Button>

        <Button 
          onPress={testBiometricAuth} 
          disabled={isLoading || !biometricState.isEnabled}
          className="bg-purple-500"
        >
          <ButtonText>
            {isLoading ? "Testing..." : "Test Biometric Auth"}
          </ButtonText>
        </Button>

        <Button 
          onPress={disableBiometric} 
          disabled={isLoading || !biometricState.isEnabled}
          variant="outline"
        >
          <ButtonText>Disable Biometric</ButtonText>
        </Button>
      </VStack>

      <Text className="text-xs text-gray-500 mt-2">
        Note: Check the console logs for detailed debugging information.
      </Text>
    </VStack>
  );
};
