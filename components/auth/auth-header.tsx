import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import { Heading } from '@/components/ui/heading';

interface AuthHeaderProps {
  title: string;
  subtitle?: string;
}

export const AuthHeader: React.FC<AuthHeaderProps> = ({ title, subtitle }) => {
  return (
    <View className="items-center mb-8">
      <Heading className="text-3xl font-dm-sans-bold text-gray-800 mb-2">
        {title}
      </Heading>
      {subtitle && (
        <Text className="text-gray-600 text-center font-dm-sans-regular">
          {subtitle}
        </Text>
      )}
    </View>
  );
};
