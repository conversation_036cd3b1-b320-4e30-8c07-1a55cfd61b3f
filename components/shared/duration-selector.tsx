import React from "react";
import { Box } from "../ui/box";
import {
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectIcon,
  SelectInput,
  SelectItem,
  SelectPortal,
  SelectScrollView,
  SelectTrigger,
} from "../ui/select";
import { Text } from "../ui/text";
import { ArrowDown2, Clock } from "iconsax-react-nativejs";
import { Duration } from "@/data/screens/activities/types";

interface DurationSelectorProps {
  value: string;
  onChange: (value: string) => void;
  durations: Duration[];
  placeholder?: string;
}

export const DurationSelector = ({
  value,
  onChange,
  durations,
  placeholder = "Select duration",
}: DurationSelectorProps) => {
  const selectedDuration = durations.find((d) => String(d.value) === value);

  return (
    <Box className="bg-white mb-2 mt-1">
      <Select selectedValue={value} onValueChange={onChange}>
        <SelectTrigger className="flex-row items-center gap-2 px-5 border-0 bg-white">
          <Clock size="20" color="black" />
          <SelectInput
            placeholder={placeholder}
            className="text-typography-700 flex-1 font-medium"
            value={selectedDuration ? selectedDuration.label : ""}
          />
          <SelectIcon
            className="mr-3"
            as={() => <ArrowDown2 size="20" color="black" />}
          />
        </SelectTrigger>
        <SelectPortal>
          <SelectBackdrop />
          <SelectContent>
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            <SelectScrollView>
              {durations.map((duration) => (
                <SelectItem
                  key={duration.value}
                  label={duration.label}
                  value={String(duration.value)}
                >
                  <Text className="text-typography-900 font-dm-sans-medium">
                    {duration.label}
                  </Text>
                </SelectItem>
              ))}
            </SelectScrollView>
          </SelectContent>
        </SelectPortal>
      </Select>
    </Box>
  );
};
