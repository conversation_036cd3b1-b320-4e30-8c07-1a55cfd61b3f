import React from "react";
import { Box } from "../ui/box";
import {
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectIcon,
  SelectInput,
  SelectItem,
  SelectPortal,
  SelectScrollView,
  SelectTrigger,
} from "../ui/select";
import { Text } from "../ui/text";
import { ArrowDown2, Clock } from "iconsax-react-nativejs";

interface DurationSelectorProps {
  value: string;
  onChange: (value: string) => void;
  durations: number[]; // in minutes
  placeholder?: string;
}

const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes} minutes`;
  }
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  if (remainingMinutes === 0) {
    return `${hours} hour${hours > 1 ? 's' : ''}`;
  }
  return `${hours} hour${hours > 1 ? 's' : ''} ${remainingMinutes} minutes`;
};

export const DurationSelector = ({
  value,
  onChange,
  durations,
  placeholder = "Select duration",
}: DurationSelectorProps) => {
  const selectedDuration = durations.find(d => String(d) === value);

  return (
    <Box className="bg-white mb-2 mt-1">
      <Select selectedValue={value} onValueChange={onChange}>
        <SelectTrigger className="flex-row items-center gap-2 px-5 border-0 bg-white">
          <Clock size="20" color="black" />
          <SelectInput
            placeholder={placeholder}
            className="text-typography-700 flex-1 font-medium"
            value={selectedDuration ? formatDuration(selectedDuration) : ""}
          />
          <SelectIcon
            className="mr-3"
            as={() => <ArrowDown2 size="20" color="black" />}
          />
        </SelectTrigger>
        <SelectPortal>
          <SelectBackdrop />
          <SelectContent>
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            <SelectScrollView>
              {durations.map((duration) => (
                <SelectItem
                  key={duration}
                  label={formatDuration(duration)}
                  value={String(duration)}
                >
                  <Text className="text-typography-900 font-dm-sans-medium">
                    {formatDuration(duration)}
                  </Text>
                </SelectItem>
              ))}
            </SelectScrollView>
          </SelectContent>
        </SelectPortal>
      </Select>
    </Box>
  );
};
