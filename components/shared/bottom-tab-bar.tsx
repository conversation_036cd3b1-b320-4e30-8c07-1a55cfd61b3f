import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Pressable } from "@/components/ui/pressable";
import { Text } from "@/components/ui/text";
import { BottomTabBarProps } from "@react-navigation/bottom-tabs";
import { HStack } from "@/components/ui/hstack";
import { Box } from "@/components/ui/box";
import { Platform } from "react-native";
import { Icon } from "@/components/ui/icon";
import { MessageCircle } from "lucide-react-native";
import { Home, More, Profile2User } from "iconsax-react-nativejs";

interface TabItem {
  name: string;
  label: string;
  path: string;
  inActiveIcon: React.ElementType;
  icon: React.ElementType;
}

const tabItems: TabItem[] = [
  {
    name: "(home)",
    label: "Home",
    path: "(home)",
    inActiveIcon: () => <Home size="30" color="#00aecc" />,
    icon: () => <Home size="30" color="#00aecc" variant="Bulk" />,
  },
  {
    name: "classes",
    label: "Classes",
    path: "classes",
    inActiveIcon: () => <Profile2User size="30" color="#00aecc" />,
    icon: () => <Profile2User size="30" color="#00aecc" variant="Bulk" />,
  },
  {
    name: "social",
    label: "Social",
    path: "social",
    inActiveIcon: MessageCircle,
    icon: MessageCircle,
  },
  {
    name: "more",
    label: "More",
    path: "more",
    inActiveIcon: () => <More size="30" color="#00aecc" />,
    icon: () => <More size="30" color="#00aecc" variant="Bulk" />,
  },
];

interface TabItemComponentProps {
  item: TabItem;
  isActive: boolean;
  onPress: () => void;
}

const TabItemComponent: React.FC<TabItemComponentProps> = ({
  item,
  isActive,
  onPress,
}: TabItemComponentProps) => (
  <Pressable className="flex-1 items-center justify-center" onPress={onPress}>
    <Icon
      as={isActive ? item.icon : item.inActiveIcon}
      size="xl"
      className={isActive ? "text-black" : "text-background-500"}
    />
    <Text
      size="xs"
      className={`mt-1 font-medium ${
        isActive ? "text-gray-400" : "text-background-500"
      }`}
    >
      {isActive ? (
        <Box className="w-2 h-2 rounded-full bg-primary-500" />
      ) : (
        item.label
      )}
    </Text>
  </Pressable>
);

export const BottomTabBar = (props: BottomTabBarProps) => {
  const insets = useSafeAreaInsets();

  return (
    <Box className="bg-background-0">
      <HStack
        className="bg-background-0 pt-4 px-7 rounded-t-3xl min-h-[78px]"
        style={{
          paddingBottom: Platform.OS === "ios" ? insets.bottom : 16,
          boxShadow: "0px -10px 12px 0px rgba(0, 0, 0, 0.04)",
        }}
        space="md"
      >
        {tabItems.map((item) => {
          const isActive =
            props.state.routeNames[props.state.index] === item.path;

          return (
            <TabItemComponent
              key={item.name}
              item={item}
              isActive={isActive}
              onPress={() => props.navigation.navigate(item.path)}
            />
          );
        })}
      </HStack>
    </Box>
  );
};
