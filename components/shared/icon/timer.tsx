import * as React from "react";
import Svg, { SvgProps, Path } from "react-native-svg";

export const TimerIcon = (props: SvgProps) => (
  <Svg width={24} height={24} fill="none" {...props}>
    <Path
      fill="#00AECC"
      fillOpacity={1}
      d="M18.351 19.68a3.63 3.63 0 0 1-3.4 2.32h-5.9a3.62 3.62 0 0 1-3.4-2.32c-.55-1.42-.17-2.99.95-4.01l4.05-3.67h2.71l4.04 3.67a3.635 3.635 0 0 1 .95 4.01Z"
      opacity={0.4}
    />
    <Path
      fill="#00AECC"
      fillOpacity={1}
      d="M13.82 18.14h-3.64a.68.68 0 0 1 0-1.36h3.64c.38 0 .68.31.68.68 0 .37-.31.68-.68.68ZM18.35 4.32A3.63 3.63 0 0 0 14.95 2h-5.9a3.63 3.63 0 0 0-2.44 6.33L10.65 12h2.71l4.04-3.67a3.635 3.635 0 0 0 .95-4.01Zm-4.53 2.91h-3.64a.68.68 0 0 1-.68-.68c0-.37.31-.68.68-.68h3.64c.38 0 .68.31.68.68 0 .37-.31.68-.68.68Z"
    />
  </Svg>
);
