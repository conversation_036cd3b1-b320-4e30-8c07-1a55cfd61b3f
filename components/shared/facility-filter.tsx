import { Box } from "../ui/box";
import {
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectIcon,
  SelectInput,
  SelectItem,
  SelectPortal,
  SelectScrollView,
  SelectTrigger,
} from "../ui/select";

import { Text } from "../ui/text";
import { useClientInfo } from "@/data/screens/common/queries/useClientConfig";
import { useMemo } from "react";
import { ArrowDown2, Location } from "iconsax-react-nativejs";

export const FacilityFilter = ({
  value,
  onChange,
}: {
  value: string;
  onChange: (value: string) => void;
}) => {
  const { data: clientData } = useClientInfo();

  const selectedFacility = useMemo(
    () =>
      clientData?.facilities.find((facility) => facility.id === Number(value)),
    [clientData?.facilities, value]
  );

  return (
    <Box className="bg-white mb-2 mt-1">
      <Select selectedValue={value} onValueChange={onChange}>
        <SelectTrigger className="flex-row items-center gap-2  px-5 border-0 bg-white">
          <Location size="20" color="black" />
          <SelectInput
            placeholder="Select location"
            className="text-typography-700 flex-1 font-medium"
            value={selectedFacility?.name}
          />
          <SelectIcon
            className="mr-3"
            as={() => <ArrowDown2 size="20" color="black" />}
          />
        </SelectTrigger>
        <SelectPortal>
          <SelectBackdrop />
          <SelectContent>
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            <SelectScrollView>
              {clientData?.facilities.map((facility) => (
                <SelectItem
                  key={facility.id}
                  label={facility.name}
                  value={String(facility.id)}
                >
                  <Text className="text-typography-900 font-dm-sans-medium">
                    {facility.name}
                  </Text>
                </SelectItem>
              ))}
            </SelectScrollView>
          </SelectContent>
        </SelectPortal>
      </Select>
    </Box>
  );
};
