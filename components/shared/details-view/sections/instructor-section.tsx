import React from "react";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Box } from "@/components/ui/box";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { router } from "expo-router";
import {
  getInitials,
  getRandomColorForInitials,
} from "@/data/common/common.utils";
import { InstructorSectionProps } from "../types";
import { Badge, BadgeText } from "@/components/ui/badge";

export const InstructorSection: React.FC<InstructorSectionProps> = ({
  instructorName,
  instructorImage,
  instructorId,
  showReadAbout = true,
  is_class_subbed,
  subbing_instructor,
}) => {
  const backgroundColor = getRandomColorForInitials(instructorName);

  const handleReadAbout = () => {
    if (instructorId) {
      router.push({
        pathname: "/trainer-details",
        params: {
          trainerId: instructorId,
          hideButton: "true",
        },
      });
    }
  };

  return (
    <HStack
      space="sm"
      className="items-center border-t border-b border-background-200 py-4 mt-2"
    >
      {instructorImage ? (
        <Avatar>
          <AvatarImage source={{ uri: instructorImage }} alt={instructorName} />
        </Avatar>
      ) : (
        <Box
          className="w-10 h-10 rounded-full items-center justify-center"
          style={{ backgroundColor }}
        >
          <Text className="text-sm font-dm-sans-bold text-white">
            {getInitials(instructorName)}
          </Text>
        </Box>
      )}

      <VStack className="flex-1" space="xs">
        <Text className="text-xs font-dm-sans-regular text-typography-500">
          Instructor
        </Text>

        {is_class_subbed ? (
          <HStack space="xs">
            <Text className="text-base font-dm-sans-bold text-typography-900">
              {subbing_instructor}{" "}
            </Text>
            <Badge size="sm" action="error" className="rounded-xl">
              <BadgeText>Sub</BadgeText>
            </Badge>
          </HStack>
        ) : (
          <Text className="text-base font-dm-sans-bold text-typography-900">
            {instructorName}
          </Text>
        )}
      </VStack>

      {showReadAbout && instructorId && (
        <Pressable onPress={handleReadAbout}>
          <Text className="text-sm font-dm-sans-medium text-[#00697B]">
            Read about
          </Text>
        </Pressable>
      )}
    </HStack>
  );
};
