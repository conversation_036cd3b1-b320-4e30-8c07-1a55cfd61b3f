import React from "react";
import { View, StatusBar, ImageBackground } from "react-native";
import { ScrollView } from "@/components/ui/scroll-view";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Box } from "@/components/ui/box";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { Icon } from "@/components/ui/icon";
import { Heart, ArrowLeft, Share2 } from "lucide-react-native";
import { router } from "expo-router";
import { BaseDetailsViewProps } from "./types";

export const BaseDetailsView: React.FC<BaseDetailsViewProps> = ({
  item,
  heroImageUrl,
  children,
  onBack,
  onShare,
  onFavorite,
  renderActionButton,
}) => {
  const statusBarHeight = StatusBar.currentHeight || 44;
  const isFavorite = item.is_favourite || false;

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.back();
    }
  };

  return (
    <View className="flex-1 bg-background-0">
      <StatusBar barStyle="light-content" />
      
      {/* Hero Image Section */}
      <View className="h-[300]">
        <ImageBackground
          source={{
            uri: heroImageUrl,
          }}
          className="h-full w-full justify-center"
          imageStyle={{
            opacity: 0.8,
            backgroundColor: "#000",
          }}
        >
          {/* Back button */}
          <Pressable
            onPress={handleBack}
            style={{
              position: "absolute",
              top: statusBarHeight + 16,
              left: 16,
              zIndex: 10,
            }}
            className="p-2 bg-slate-500 rounded-full"
          >
            <Icon
              as={ArrowLeft}
              size="sm"
              color="white"
              className="text-typography-900"
            />
          </Pressable>
        </ImageBackground>

        {/* Share and favorite buttons */}
        <HStack
          style={{
            position: "absolute",
            top: statusBarHeight + 16,
            right: 16,
            zIndex: 10,
          }}
          space="sm"
        >
          <Pressable
            onPress={onFavorite}
            className="p-2 bg-slate-500 rounded-full"
          >
            <Icon
              as={Heart}
              size="sm"
              color="white"
              className={
                isFavorite ? "text-error-500 fill-error-500" : "text-white"
              }
            />
          </Pressable>
          <Pressable onPress={onShare} className="p-2 bg-slate-500 rounded-full">
            <Icon
              as={Share2}
              color="white"
              size="sm"
              className="text-typography-900"
            />
          </Pressable>
        </HStack>

        {/* Title overlay at bottom of image section */}
        <Box className="absolute bottom-0 left-0 right-0 p-6 pb-8">
          <Text className="text-4xl font-dm-sans-bold text-white">
            {item.name}
          </Text>
        </Box>
      </View>

      {/* Content card that overlays the bottom portion of the image */}
      <Box className="bg-background-0 rounded-t-3xl -mt-5 flex-1">
        <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
          <VStack space="lg" className="px-4 pt-6 pb-24">
            {children}
          </VStack>
        </ScrollView>

        {/* Action button section */}
        {renderActionButton && (
          <Box className="px-6 pt-2 pb-6 bg-white border-t border-background-200">
            {renderActionButton()}
          </Box>
        )}
      </Box>
    </View>
  );
};
