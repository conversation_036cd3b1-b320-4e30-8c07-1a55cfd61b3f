import { ReactNode } from "react";

export interface BaseDetailsItem {
  id: number;
  name: string;
  description: string;
  start_time: string;
  end_time: string;
  gym_name: string;
  room_name: string;
  is_favourite?: boolean;
  is_class_subbed?: boolean;
  subbing_instructor?: string;
}

// Extended interface for classes
export interface ClassDetailsItem extends BaseDetailsItem {
  instructor_first_name: string;
  instructor_last_name: string;
  instructor_id: number;
  instructor_image?: string;
  images: string[];
  category: string;
  class_type: "Virtual" | "Live" | "Virtual & Live";
  spots: number;
  reservation_count: number;
}

// Extended interface for events
export interface EventDetailsItem extends BaseDetailsItem {
  image_url?: string;
  price: string;
  member_price: string;
  is_paid: number;
  spots: number;
  reservation_count: number;
}

// Props for the base details view component
export interface BaseDetailsViewProps {
  item: BaseDetailsItem;
  selectedDate?: string;
  isLoading?: boolean;
  heroImageUrl?: string;
  children: ReactNode;
  onBack?: () => void;
  onShare?: () => void;
  onFavorite?: () => void;
  renderActionButton?: () => ReactNode;
}

// Props for composable sections
export interface MetricsSectionProps {
  intensity?: {
    label: string;
    color: string;
  };
  duration: string;
  showIntensity?: boolean;
}

export interface InfoSectionProps {
  date: string;
  timeFrame: string;
  spotsLeft: number;
  location: string;
  room: string;
  additionalInfo?: Array<{
    icon: ReactNode;
    text: string;
  }>;
}

export interface InstructorSectionProps {
  instructorName: string;
  instructorImage?: string;
  instructorId?: number;
  showReadAbout?: boolean;
  subbing_instructor?: string;
  is_class_subbed?: boolean;
}

export interface DescriptionSectionProps {
  title?: string;
  description: string;
}

export interface ActionSectionProps {
  children: ReactNode;
}
