import { SearchNormal1 } from "iconsax-react-nativejs";
import { HStack } from "../ui/hstack";
import { Input, InputField, InputIcon, InputSlot } from "../ui/input";
import { Pressable } from "react-native";
import { Icon } from "../ui/icon";
import { SlidersHorizontal } from "lucide-react-native";

export const SearchInput = ({
  onSearch,
  searchTerm,
  placeholder = "Search",
}: {
  onSearch?: (text: string) => void;
  searchTerm?: string;
  placeholder?: string;
}) => {
  return (
    <HStack className="gap-2 pl-4 pr-4">
      <Input className="h-10 flex-1 rounded-lg bg-white border" size="md">
        <InputSlot className="pl-4">
          <InputIcon
            as={() => <SearchNormal1 size="20" color="gray" />}
            className="text-typography-400"
            size="md"
            color="gray"
          />
        </InputSlot>
        <InputField
          placeholder={placeholder}
          className="placeholder:text-typography-400 focus:border-none"
          onChangeText={onSearch}
          value={searchTerm}
        />
      </Input>
      <Pressable className="w-10 h-10 bg-background-50 rounded-full items-center justify-center border border-outline-200">
        <Icon
          color="gray"
          as={SlidersHorizontal}
          className="text-typography-600"
          size="md"
        />
      </Pressable>
    </HStack>
  );
};
