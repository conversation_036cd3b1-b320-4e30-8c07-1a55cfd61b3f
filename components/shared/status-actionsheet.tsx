import React from "react";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import { Box } from "@/components/ui/box";
import { Icon } from "@/components/ui/icon";
import { Check } from "lucide-react-native";
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from "@/components/ui/actionsheet";
import { Danger } from "iconsax-react-nativejs";

export type StatusType = "success" | "error";

export interface ActionButton {
  label: string;
  onPress: () => void;
  variant?: "primary" | "secondary" | "outline";
  className?: string;
}

interface StatusActionSheetProps {
  isOpen: boolean;
  onClose: () => void;
  status: StatusType;
  title: string;
  description: string;
  actions: ActionButton[];
  className?: string;
}

const getStatusConfig = (status: StatusType) => {
  switch (status) {
    case "success":
      return {
        icon: Check,
        iconBgColor: "bg-green-400",
        iconColor: "text-base",
      };
    case "error":
      return {
        icon: () => <Danger size="20" color="#700E0B" strokeWidth={2} />,
        iconBgColor: "bg-red-300",
        iconColor: "text-red-500",
      };
    default:
      return {
        icon: Check,
        iconBgColor: "bg-green-100",
        iconColor: "text-green-500",
      };
  }
};

export const StatusActionSheet: React.FC<StatusActionSheetProps> = ({
  isOpen,
  onClose,
  status,
  title,
  description,
  actions,
  className,
}) => {
  const getButtonStyles = (variant: ActionButton["variant"] = "primary") => {
    switch (variant) {
      case "primary":
        return {
          buttonClass: "bg-[#00BFE0] rounded-full",
          textClass: "text-black font-dm-sans-medium text-base",
        };
      case "secondary":
        return {
          buttonClass: "border-[#00BFE0] rounded-full bg-[#E6F9FC]",
          textClass: "text-[#00BFE0] font-dm-sans-medium text-base",
        };
      case "outline":
        return {
          buttonClass:
            "border border-typography-300 bg-transparent rounded-full",
          textClass: "text-typography-600 font-dm-sans-medium text-base",
        };
      default:
        return {
          buttonClass: "bg-[#00BFE0] rounded-full",
          textClass: "text-black font-dm-sans-medium text-base",
        };
    }
  };

  const statusConfig = getStatusConfig(status);

  return (
    <Actionsheet isOpen={isOpen} onClose={onClose} className={className}>
      <ActionsheetBackdrop className="bg-[#000] opacity-50" />
      <ActionsheetContent>
        <ActionsheetDragIndicatorWrapper>
          <ActionsheetDragIndicator />
        </ActionsheetDragIndicatorWrapper>

        <VStack space="lg" className="px-6 py-8 items-center">
          <Box
            className={`w-14 h-14 rounded-full ${statusConfig.iconBgColor} items-center justify-center mb-4`}
          >
            <Icon
              as={statusConfig.icon}
              size="md"
              className={statusConfig.iconColor}
              color="red"
            />
          </Box>

          <Text className="text-lg font-dm-sans-bold text-typography-900 text-center">
            {title}
          </Text>

          <Text className="text-base font-dm-sans-regular text-typography-600 text-center px-4">
            {description}
          </Text>

          <VStack space="sm" className="w-full mt-4">
            {actions.map((action, index) => {
              const buttonStyles = getButtonStyles(action.variant);
              return (
                <Button
                  key={index}
                  onPress={action.onPress}
                  className={`${buttonStyles.buttonClass} ${action.className || ""}`}
                  variant={action.variant === "outline" ? "outline" : "solid"}
                >
                  <ButtonText className={buttonStyles.textClass}>
                    {action.label}
                  </ButtonText>
                </Button>
              );
            })}
          </VStack>
        </VStack>
      </ActionsheetContent>
    </Actionsheet>
  );
};
