import React from "react";
import { Box } from "../ui/box";
import {
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectIcon,
  SelectInput,
  SelectItem,
  SelectPortal,
  SelectScrollView,
  SelectTrigger,
} from "../ui/select";
import { Text } from "../ui/text";
import { ArrowDown2, Clock } from "iconsax-react-nativejs";
import { AvailableTimeSlot } from "@/data/screens/activities/types";

interface TimeSlotSelectorProps {
  value: string;
  onChange: (value: string) => void;
  timeSlots: AvailableTimeSlot[];
  placeholder?: string;
}

export const TimeSlotSelector = ({
  value,
  onChange,
  timeSlots,
  placeholder = "Select time",
}: TimeSlotSelectorProps) => {
  const selectedSlot = timeSlots.find((slot) => slot.time_value === value);

  return (
    <Box className="bg-white mb-2 mt-1">
      <Select selectedValue={value} onValueChange={onChange}>
        <SelectTrigger className="flex-row items-center gap-2 px-5 border-0 bg-white">
          <Clock size="20" color="black" />
          <SelectInput
            placeholder={placeholder}
            className="text-typography-700 flex-1 font-medium"
            value={selectedSlot ? selectedSlot.time_label : ""}
          />
          <SelectIcon
            className="mr-3"
            as={() => <ArrowDown2 size="20" color="black" />}
          />
        </SelectTrigger>
        <SelectPortal>
          <SelectBackdrop />
          <SelectContent>
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            <SelectScrollView>
              {timeSlots
                .filter((slot) => slot.allow_reservations)
                .map((slot) => (
                  <SelectItem
                    key={slot.time_value}
                    label={slot.time_label}
                    value={slot.time_value}
                  >
                    <Text className="text-typography-900 font-dm-sans-medium">
                      {slot.time_label}
                    </Text>
                  </SelectItem>
                ))}
            </SelectScrollView>
          </SelectContent>
        </SelectPortal>
      </Select>
    </Box>
  );
};
