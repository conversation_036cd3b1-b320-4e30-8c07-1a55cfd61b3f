import React from "react";
import { Box } from "../ui/box";
import {
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectIcon,
  SelectInput,
  SelectItem,
  SelectPortal,
  SelectScrollView,
  SelectTrigger,
} from "../ui/select";
import { Text } from "../ui/text";
import { ArrowDown2, Clock } from "iconsax-react-nativejs";
import { format } from "date-fns";

interface TimeSlotSelectorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

// Generate time slots from 6 AM to 10 PM in 30-minute intervals
const generateTimeSlots = (): string[] => {
  const slots: string[] = [];
  const startHour = 6; // 6 AM
  const endHour = 22; // 10 PM
  
  for (let hour = startHour; hour < endHour; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const time = new Date();
      time.setHours(hour, minute, 0, 0);
      slots.push(format(time, "HH:mm"));
    }
  }
  
  return slots;
};

const formatTimeSlot = (time: string): string => {
  if (!time) return "";
  const [hours, minutes] = time.split(":");
  const date = new Date();
  date.setHours(parseInt(hours), parseInt(minutes), 0, 0);
  return format(date, "h:mm a");
};

export const TimeSlotSelector = ({
  value,
  onChange,
  placeholder = "Select time",
}: TimeSlotSelectorProps) => {
  const timeSlots = generateTimeSlots();

  return (
    <Box className="bg-white mb-2 mt-1">
      <Select selectedValue={value} onValueChange={onChange}>
        <SelectTrigger className="flex-row items-center gap-2 px-5 border-0 bg-white">
          <Clock size="20" color="black" />
          <SelectInput
            placeholder={placeholder}
            className="text-typography-700 flex-1 font-medium"
            value={value ? formatTimeSlot(value) : ""}
          />
          <SelectIcon
            className="mr-3"
            as={() => <ArrowDown2 size="20" color="black" />}
          />
        </SelectTrigger>
        <SelectPortal>
          <SelectBackdrop />
          <SelectContent>
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            <SelectScrollView>
              {timeSlots.map((slot) => (
                <SelectItem
                  key={slot}
                  label={formatTimeSlot(slot)}
                  value={slot}
                >
                  <Text className="text-typography-900 font-dm-sans-medium">
                    {formatTimeSlot(slot)}
                  </Text>
                </SelectItem>
              ))}
            </SelectScrollView>
          </SelectContent>
        </SelectPortal>
      </Select>
    </Box>
  );
};
