import React from "react";
import { FlatList, RefreshControl } from "react-native";
import { CardSkeletonLoader } from "@/components/shared/card-skeleton";
import { EmptyState } from "@/components/screens/classes/empty-state";
import { ClassDetailsResponse } from "@/data/screens/classes/types";
import { CategoryType } from "@/data/screens/appointments/types";
import { CategoryCard } from "../screens/appointments/category-card";
import {
  ActivityCard,
  ActivityCardData,
} from "@/components/shared/activity-card";
import { ClassStatusButton } from "@/components/screens/classes/class-card/class-button";
import { formatDate } from "@/data/common/common.utils";
import {
  obtainSpotsAvailable,
  obtainStatus,
} from "@/data/screens/classes/utils";
import { router } from "expo-router";
import { useFavoriteMutation } from "@/data/screens/common/queries/useFavoriteMutation";
import { EmptySearchState } from "../shared/empty-search";

interface ClassesListProps {
  data: ClassDetailsResponse[] | CategoryType[] | undefined;
  selectedTab: "classes" | "appointment";
  isLoading: boolean;
  isRefreshing: boolean;
  searchTerm: string;
  contentBottomPadding: number;
  onRefresh: () => void;
  onClearSearch: () => void;
  selectedDate: Date;
  selectedFacility?: string;
}

const convertClassToActivityCard = (
  classData: ClassDetailsResponse
): ActivityCardData => ({
  id: String(classData.id),
  title: classData.name,
  imageUrl: classData.images?.[0],
  startTime: classData.start_time,
  endTime: classData.end_time,
  spotsLeft: obtainSpotsAvailable(classData.class_type, classData) || 0,
  instructor: classData.is_class_subbed
    ? classData.subbing_instructor
    : `${classData.instructor_first_name} ${classData.instructor_last_name}`,
  location: classData.gym_name,
  status: obtainStatus(classData.class_type),
  isFavorite: classData.is_favourite || false,
});

const RenderEmptyState = ({
  isLoading,
  isEmpty,
  searchTerm,
  onClearSearch,
}: {
  isLoading: boolean;
  isEmpty: boolean;
  searchTerm?: string;
  onClearSearch: () => void;
  contentBottomPadding: number;
}) => {
  if (isLoading) {
    return <CardSkeletonLoader />;
  }

  if (isEmpty && searchTerm) {
    return (
      <EmptySearchState searchTerm={searchTerm} onClearSearch={onClearSearch} />
    );
  }

  if (isEmpty) {
    return (
      <EmptyState
        title="No classes available"
        subtitle="There are no classes scheduled for this date. Please select a different date."
      />
    );
  }

  return null;
};

const RenderItem = ({
  item,
  selectedTab,
  selectedDate,
  selectedFacility,
}: {
  item: ClassDetailsResponse | CategoryType;
  selectedTab: "classes" | "appointment";
  selectedDate: Date;
  selectedFacility?: string;
}) => {
  const favoriteMutation = useFavoriteMutation();

  if (selectedTab === "classes") {
    const activityData = convertClassToActivityCard(
      item as ClassDetailsResponse
    );

    const handleFavoritePress = () => {
      favoriteMutation.mutate({
        type: "class",
        item_id: item.id,
      });
    };

    return (
      <ActivityCard
        data={activityData}
        onPress={() => {
          router.push({
            pathname: "/(class-details)/[id]",
            params: {
              id: item.id,
              date: formatDate(selectedDate),
              gym_id: selectedFacility,
            },
          });
        }}
        onFavoritePress={handleFavoritePress}
        showInstructor={true}
        renderButton={() => (
          <ClassStatusButton
            data={item as ClassDetailsResponse}
            selectedDate={selectedDate}
          />
        )}
      />
    );
  } else {
    return <CategoryCard {...(item as CategoryType)} />;
  }
};

export const ClassesList: React.FC<ClassesListProps> = ({
  data,
  selectedTab,
  isLoading,
  isRefreshing,
  searchTerm,
  contentBottomPadding,
  onRefresh,
  onClearSearch,
  selectedDate,
  selectedFacility,
}) => {
  const isEmpty = !data?.length;

  if (isLoading || isEmpty) {
    return (
      <RenderEmptyState
        isLoading={isLoading}
        isEmpty={isEmpty}
        searchTerm={searchTerm}
        onClearSearch={onClearSearch}
        contentBottomPadding={contentBottomPadding}
      />
    );
  }

  return (
    <FlatList
      className="pt-5"
      data={data}
      renderItem={({ item }) => (
        <RenderItem
          item={item}
          selectedTab={selectedTab}
          selectedDate={selectedDate}
          selectedFacility={selectedFacility}
        />
      )}
      keyExtractor={(item: ClassDetailsResponse | CategoryType) =>
        String(item.id)
      }
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={onRefresh}
          tintColor="#00697B"
          colors={["#00697B"]}
        />
      }
      contentContainerStyle={{
        paddingBottom: 600,
      }}
    />
  );
};
