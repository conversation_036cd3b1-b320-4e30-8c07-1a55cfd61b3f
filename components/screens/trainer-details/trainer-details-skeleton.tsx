import React from "react";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";

import { ScrollView, StatusBar, View } from "react-native";
import { Pressable } from "@/components/ui/pressable";
import { Icon } from "@/components/ui/icon";
import { ArrowLeft } from "lucide-react-native";
import { router } from "expo-router";

import { Box } from "@/components/ui/box";
import { Skeleton, SkeletonText } from "@/components/ui/skeleton";

export const TrainerDetailsSkeleton = () => {
  const statusBarHeight = StatusBar.currentHeight || 44;

  return (
    <View className="flex-1 bg-background-0">
      {/* Header Image Skeleton */}
      <View className="h-[370px] w-full">
        <StatusBar barStyle="light-content" />
        <Skeleton className="h-full w-full bg-background-300">
          <Pressable
            onPress={() => router.back()}
            style={{
              position: "absolute",
              top: statusBarHeight + 16,
              left: 16,
              zIndex: 10,
            }}
            className="p-2 bg-slate-500 rounded-full"
          >
            <Icon
              as={ArrowLeft}
              size="sm"
              color="white"
              className="text-typography-900"
            />
          </Pressable>
        </Skeleton>
      </View>

      <Box className="bg-white rounded-t-3xl -mt-2 flex-1">
        <ScrollView
          contentContainerStyle={{ paddingBottom: 120 }}
          showsVerticalScrollIndicator={false}
        >
          <VStack className="p-6" space="lg">
            {/* Name and Experience Skeleton */}
            <VStack space="xs">
              <SkeletonText className="h-8 w-48 rounded" />
              <SkeletonText className="h-5 w-40 rounded" />
            </VStack>

            {/* Expertise Section Skeleton */}
            <VStack space="sm">
              <SkeletonText className="h-5 w-20 rounded" />
              <HStack space="sm" className="flex-wrap">
                {/* Specialty badges skeleton */}
                <Skeleton className="h-8 w-24 rounded-lg" />
                <Skeleton className="h-8 w-32 rounded-lg" />
                <Skeleton className="h-8 w-28 rounded-lg" />
                <Skeleton className="h-8 w-36 rounded-lg" />
              </HStack>
            </VStack>

            {/* Description Section Skeleton */}
            <VStack space="sm">
              <SkeletonText className="h-5 w-24 rounded" />
              <VStack space="xs">
                <SkeletonText className="h-4 w-full rounded" />
                <SkeletonText className="h-4 w-full rounded" />
                <SkeletonText className="h-4 w-3/4 rounded" />
                <SkeletonText className="h-4 w-full rounded" />
                <SkeletonText className="h-4 w-5/6 rounded" />
              </VStack>
            </VStack>
          </VStack>
        </ScrollView>
      </Box>
    </View>
  );
};
