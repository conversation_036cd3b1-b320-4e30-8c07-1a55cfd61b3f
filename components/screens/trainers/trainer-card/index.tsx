import React from "react";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { Button, ButtonText } from "@/components/ui/button";
import { TouchableOpacity, View } from "react-native";
import { router } from "expo-router";
import { Trainer } from "@/data/screens/appointments/types";
import { getInitials } from "@/data/common/common.utils";
import { COLOR_CODES } from "@/constants/color-codes";
import { truncateText } from "@/utils/common";

export const TrainerCard: React.FC<Trainer> = ({
  id,
  first_name,
  last_name,
  years_experience,
  specialties,
  profile_image,
}) => {
  const handleCardPress = () => {
    router.push({
      pathname: "/trainer-details",
      params: { trainerId: id },
    });
  };

  const fullName = `${first_name} ${last_name}`;
  const remainingCount = specialties.length - 3;

  return (
    <TouchableOpacity onPress={handleCardPress} activeOpacity={0.7}>
      <View className="bg-white rounded-2xl p-4 border border-background-200 mb-3 shadow-sm">
        <HStack space="md" className="items-start">
          {profile_image ? (
            <Avatar size="xl" className="w-20 h-20">
              <AvatarImage source={{ uri: profile_image }} alt={fullName} />
            </Avatar>
          ) : (
            <View
              className="w-20 h-20 rounded-full items-center justify-center"
              style={{ backgroundColor: COLOR_CODES.trainer.circle }}
            >
              <Text
                className={`text-[${COLOR_CODES.trainer.text}] text-xl font-dm-sans-bold`}
              >
                {getInitials(fullName)}
              </Text>
            </View>
          )}

          <VStack className="flex-1" space="xs">
            <HStack className="justify-between items-center">
              <Text
                numberOfLines={2}
                className="text-[#00889F] text-sm font-dm-sans-bold line-clamp-1 max-w-36"
              >
                {truncateText(fullName, 20)}
              </Text>
              <HStack>
                <Button
                  variant="solid"
                  size="xs"
                  className="rounded-full bg-[##E6F9FC]"
                >
                  <ButtonText className="text-[#00697B] font-dm-sans-medium text-xs">
                    Book
                  </ButtonText>
                </Button>
              </HStack>
            </HStack>

            <Text className="text-typography-500 text-xs">
              {years_experience} years of experience
            </Text>

            <VStack space="xs" className="mt-2">
              <HStack space="xs" className="flex-wrap">
                {specialties.slice(0, 3).map((specialty, index) => (
                  <View
                    key={index}
                    className="px-2 py-1.5 rounded-lg border border-background-200"
                  >
                    <Text className="text-typography-600 text-xs font-dm-sans-regular">
                      {truncateText(specialty.name, 15)}
                    </Text>
                  </View>
                ))}

                {remainingCount > 0 && (
                  <View className="px-2 py-1.5 rounded-lg border border-background-200">
                    <Text className="text-typography-600 text-xs font-dm-sans-regular">
                      +{remainingCount}
                    </Text>
                  </View>
                )}
              </HStack>
            </VStack>
          </VStack>
        </HStack>
      </View>
    </TouchableOpacity>
  );
};
