import React from "react";
import { FlatList, RefreshControl } from "react-native";
import { EventResponse } from "@/data/screens/events/types";

import {
  ActivityCard,
  ActivityCardData,
} from "@/components/shared/activity-card";
import { StatusButton } from "@/components/shared/status-button";
import { CardSkeletonLoader } from "../../shared/card-skeleton";
import { EmptySearchState } from "@/components/shared/empty-search";
import { EmptyState } from "../classes/empty-state";
import { uniqueId } from "lodash/fp";
import { useFavoriteMutation } from "@/data/screens/common/queries/useFavoriteMutation";
import { router } from "expo-router";
import { formatDate } from "@/data/common/common.utils";

interface EventsListProps {
  events: EventResponse[];
  isLoading: boolean;
  isRefreshing: boolean;
  searchTerm: string;
  selectedDate: Date;
  onRefresh: () => void;
  onClearSearch: () => void;
}

const convertEventToActivityCard = (
  event: EventResponse
): ActivityCardData => ({
  id: String(event.id),
  title: event.name,
  imageUrl: event.image_url || undefined,
  startTime: event.start_time,
  endTime: event.end_time,
  spotsLeft: Math.max(0, event.spots - event.reservation_count),
  instructor: "", // Events don't have instructors in this API
  location: `${event.room_name}, ${event.gym_name}`,
  isFavorite: event.is_favourite,
});

const getStatusButtonConfig = (event: EventResponse) => {
  const spotsLeft = Math.max(0, event.spots - event.reservation_count);
  const hasReservation = event.reservations && event.reservations.length > 0;

  // If user has a reservation, show cancel option
  if (hasReservation) {
    return {
      variant: "cancel_reservation" as const,
      text: "Cancel reservation",
      disabled: false,
    };
  }

  // If event is not active, show cancelled
  if (!event.active) {
    return {
      variant: "event_cancelled" as const,
      text: "Event cancelled",
      disabled: true,
    };
  }

  // If no spots left, check if waitlist is allowed
  if (spotsLeft === 0) {
    if (event.allow_waitlist) {
      return {
        variant: "join_waitlist" as const,
        text: "Join waitlist",
        disabled: false,
      };
    } else {
      return {
        variant: "event_full" as const,
        text: "Event full",
        disabled: true,
      };
    }
  }

  // Default case - allow reservation
  return { variant: "reserve" as const, text: "Reserve", disabled: false };
};

export const EventsList = ({
  events,
  isLoading,
  isRefreshing,
  searchTerm,
  selectedDate,
  onRefresh,
  onClearSearch,
}: EventsListProps) => {
  const { mutate: favoriteMutation } = useFavoriteMutation();
  if (isLoading) {
    return <CardSkeletonLoader />;
  }

  if (events.length === 0 && searchTerm) {
    return (
      <EmptySearchState searchTerm={searchTerm} onClearSearch={onClearSearch} />
    );
  }

  if (!events.length) {
    return (
      <EmptyState
        title="No events available"
        subtitle="There are no events scheduled for this date. Please select a different date."
      />
    );
  }

  return (
    <FlatList
      data={events}
      keyExtractor={(item) => uniqueId(String(item.id))}
      renderItem={({ item }) => {
        const activityData = convertEventToActivityCard(item);
        const buttonConfig = getStatusButtonConfig(item);

        return (
          <ActivityCard
            data={activityData}
            onPress={() =>
              router.push({
                pathname: "/(event-details)/[id]",
                params: { id: item.id, date: formatDate(selectedDate) },
              })
            }
            onFavoritePress={() =>
              favoriteMutation({
                type: "class",
                item_id: item.id,
              })
            }
            showInstructor={false}
            renderButton={() => (
              <StatusButton
                variant={buttonConfig.variant}
                text={buttonConfig.text}
                disabled={buttonConfig.disabled}
                size="sm"
              />
            )}
          />
        );
      }}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
      }
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{
        paddingTop: 16,
        paddingBottom: 600,
      }}
    />
  );
};
