import React from "react";
import { EventResponse } from "@/data/screens/events/types";
import { obtainDateFrame } from "@/data/common/common.utils";
import { format, parseISO } from "date-fns";
import { DATE_FORMAT } from "@/constants/date-formats";
import { useFavoriteMutation } from "@/data/screens/common/queries/useFavoriteMutation";
import { EventDetailsSkeleton } from "./event-details-skeleton";
import {
  BaseDetailsView,
  InfoSection,
  DescriptionSection,
} from "@/components/shared/details-view";
import { StatusButton } from "@/components/shared/status-button";
import { Icon } from "@/components/ui/icon";
import { DollarSign } from "lucide-react-native";

interface EventDetailsProps {
  eventItem: EventResponse;
  selectedDate?: string;
  isLoading?: boolean;
}

export const EventDetails: React.FC<EventDetailsProps> = ({
  eventItem,
  selectedDate,
  isLoading = false,
}) => {
  const { mutate: favoriteMutation } = useFavoriteMutation();

  if (isLoading) {
    return <EventDetailsSkeleton />;
  }

  const selectedEventDate = selectedDate ?? new Date().toISOString();

  const handleFavorite = () => {
    favoriteMutation({
      type: "class", // Events use the same endpoint as classes
      item_id: eventItem.id,
    });
  };

  // Get event status for button
  const getEventStatus = () => {
    const spotsLeft = Math.max(
      0,
      eventItem.spots - eventItem.reservation_count
    );

    if (spotsLeft === 0) {
      return {
        variant: "outline" as const,
        text: "Event Full",
        disabled: true,
      };
    }

    return {
      variant: "solid" as const,
      text: eventItem.is_paid ? `Reserve - $${eventItem.price}` : "Reserve",
      disabled: false,
    };
  };

  const statusConfig = getEventStatus();
  const spotsLeft = Math.max(0, eventItem.spots - eventItem.reservation_count);

  // Additional info for events (price if paid)
  const additionalInfo = [];
  if (eventItem.is_paid) {
    additionalInfo.push({
      icon: <Icon as={DollarSign} size="sm" className="text-typography-600" />,
      text: `$${eventItem.price} (Member: $${eventItem.member_price})`,
    });
  }

  return (
    <BaseDetailsView
      item={eventItem}
      heroImageUrl={eventItem.image_url || undefined}
      onFavorite={handleFavorite}
      renderActionButton={() => (
        <StatusButton
          variant={"reserve"}
          text={statusConfig.text}
          disabled={statusConfig.disabled}
          size="lg"
        />
      )}
    >
      <InfoSection
        date={format(
          parseISO(selectedEventDate),
          DATE_FORMAT.DAY_MONTH_DAY_YEAR
        )}
        timeFrame={obtainDateFrame(eventItem.start_time, eventItem.end_time)}
        spotsLeft={spotsLeft}
        location={eventItem.gym_name}
        room={eventItem.room_name}
        additionalInfo={additionalInfo}
      />

      <DescriptionSection description={eventItem.description} />
    </BaseDetailsView>
  );
};

export default EventDetails;
