import React from "react";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { ChevronLeft } from "lucide-react-native";
import { Icon } from "@/components/ui/icon";
import { router } from "expo-router";

export const EventsHeader = () => {
  const handleBackPress = () => router.back();

  return (
    <VStack space="md" className="px-4 pt-4 pb-2">
      <HStack className="justify-between items-center">
        <HStack className="items-center" space="md">
          <Pressable
            onPress={handleBackPress}
            className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center"
          >
            <Icon as={ChevronLeft} size="lg" className="text-typography-900" />
          </Pressable>
          <Text className="text-lg font-dm-sans-bold text-typography-900">
            Events
          </Text>
        </HStack>
      </HStack>
    </VStack>
  );
};
