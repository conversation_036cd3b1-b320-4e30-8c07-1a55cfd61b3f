import React from "react";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { SlidersHorizontal } from "lucide-react-native";
import { Icon } from "@/components/ui/icon";

const ClassesHeader = () => {
  return (
    <VStack space="md" className="px-4 pt-4 pb-2">
      <HStack className="justify-between items-center">
        <Text className="text-lg font-dm-sans-bold text-typography-900">
          Classes & Appointments
        </Text>
        <Pressable className="p-2">
          <Icon
            as={SlidersHorizontal}
            size="lg"
            className="text-typography-600"
          />
        </Pressable>
      </HStack>
    </VStack>
  );
};

export default ClassesHeader;
