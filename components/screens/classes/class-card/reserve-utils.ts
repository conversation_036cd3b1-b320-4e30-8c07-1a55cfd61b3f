import { ActionButton } from "@/components/shared/status-actionsheet";
// import { ClassDetailsResponse } from "@/data/screens/classes/types";
// import { createShareText } from "@/utils/calendar";
import { noop } from "lodash/fp";

export const reserveActions =
  () // data: ClassDetailsResponse & { selectedDate: Date }
  : ActionButton[] => {
    return [
      {
        label: "Make another reservation",
        onPress: noop,
        variant: "primary",
      },
      {
        label: "Add to calendar",
        onPress: noop,
        variant: "secondary",
      },
      {
        label: "Share with friends",
        onPress: noop,
        // onPress: () =>
        //   createShareText({
        //     title: data.name,
        //     startTime: data.start_time,
        //     endTime: data.end_time,
        //     date: data.selected_date ?? "",
        //     location: `${data?.gym_name}, ${data?.room_name}`,
        //     instructor: `${data?.instructor_first_name} ${data?.instructor_last_name}`,
        //   }),
        variant: "outline",
      },
    ];
  };
