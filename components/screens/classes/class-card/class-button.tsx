import React, { useMemo, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonText } from "@/components/ui/button"; // Adjust import path
import { ClassDetailsResponse } from "@/data/screens/classes/types";
import {
  actionDisabled,
  getButtonClass,
  getButtonText,
  getClassAvailabilityAction,
} from "./action-button-utils";
import { useReserveClass } from "@/data/screens/classes/mutations/useReserveClass";
import { useCancelReservation } from "@/data/screens/classes/mutations/useCancelReservation";
import { StatusActionSheet } from "@/components/shared/status-actionsheet";
import { reserveActions } from "./reserve-utils";
import { ClassActionState } from "./types";
import { formatDate } from "@/data/common/common.utils";
import { noop } from "lodash/fp";

type ClassButtonProps = {
  data: ClassDetailsResponse;
  selectedDate?: Date | string;
  size?: "sm" | "md" | "lg";
};

export const ClassStatusButton = ({
  data,
  selectedDate,
  size = "sm",
}: ClassButtonProps) => {
  const availability = useMemo(() => getClassAvailabilityAction(data), [data]);

  const [showToast, setShowToast] = useState(false);

  const {
    mutate: reserveClass,
    isPending: isReserving,
    error,
  } = useReserveClass(() => {
    setShowToast(true);
  });

  const { mutate: cancelReservation, isPending: isCancelling } =
    useCancelReservation();

  if (!availability) return null;

  const { actionState } = availability;

  const buttonText = getButtonText(actionState);
  const classNames = getButtonClass(actionState);

  const disabled = actionDisabled(actionState);

  const isLoading = isReserving || isCancelling;

  const handlePress = (actionState: ClassActionState) => {
    switch (actionState) {
      case ClassActionState.CAN_RESERVE_BOTH:
      case ClassActionState.CAN_RESERVE_LIVE:
        return reserveClass({
          class_id: data.id,
          date: formatDate(selectedDate),
          is_virtual: false,
        });
      case ClassActionState.CAN_RESERVE_VIRTUAL:
        return reserveClass({
          class_id: data.id,
          date: formatDate(selectedDate),
          is_virtual: true,
        });
      case ClassActionState.USER_HAS_RESERVATION:
        if (data?.current_user_reservation?.id) {
          return cancelReservation(data.current_user_reservation.id as number);
        }
        return noop;
      default:
        return noop;
    }
  };

  return (
    <>
      <Button
        size={size}
        variant={"outline"}
        className={`rounded-full ${classNames}`}
        disabled={disabled}
        onPress={(e) => {
          e.stopPropagation();
          handlePress(actionState);
        }}
      >
        <ButtonText className={`${classNames}`}>{buttonText}</ButtonText>
        {isLoading && <ButtonSpinner color="gray" className="ml-2" />}
      </Button>

      {showToast && (
        <StatusActionSheet
          isOpen={showToast}
          onClose={() => setShowToast(false)}
          status={error ? "error" : "success"}
          title={error ? "Error occurred" : "Reservation made"}
          description={error?.message ?? ""}
          actions={error ? [] : reserveActions()}
        />
      )}
    </>
  );
};
