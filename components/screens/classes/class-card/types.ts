export enum ClassActionState {
  // User already engaged
  USER_HAS_RESERVATION = "USER_HAS_RESERVATION",
  USER_ON_WAITLIST = "USER_ON_WAITLIST",

  // Available actions
  CAN_RESERVE_LIVE = "CAN_RESERVE_LIVE",
  CAN_RESERVE_VIRTUAL = "CAN_RESERVE_VIRTUAL",
  CAN_RESERVE_BOTH = "CAN_RESERVE_BOTH", // Hybrid classes
  CAN_JOIN_WAITLIST = "CAN_JOIN_WAITLIST",

  // Full/unavailable states
  FULL_WITH_WALKINS = "FULL_WITH_WALKINS",
  FULL_WITH_WAITLIST = "FULL_WITH_WAITLIST",
  FULL_NO_OPTIONS = "FULL_NO_OPTIONS",

  // Unavailable states
  CLASS_PAST = "CLASS_PAST",
  CLASS_CANCELLED = "CLASS_CANCELLED",
  RESERVATIONS_DISABLED = "RESERVATIONS_DISABLED",
  NO_ACTION_AVAILABLE = "NO_ACTION_AVAILABLE",
}

export interface ClassAvailabilityAction {
  actionState: ClassActionState;
  // Keep some metadata that might be useful for UI
  metadata?: {
    liveSpots?: number;
    virtualSpots?: number;
    hasWaitlist?: boolean;
  };
}

// Domain models for better understanding
export interface ClassState {
  isPast: boolean;
  isCancelled: boolean;
  isReservationAllowed: boolean;
  isFull: boolean;
  hasWalkinSpots: boolean;
}

export interface UserStatus {
  hasReservation: boolean;
  isOnWaitlist: boolean;
}

export interface SpotAvailability {
  liveSpots: number;
  virtualSpots: number;
  hasLiveSpots: boolean;
  hasVirtualSpots: boolean;
}

export interface WaitlistInfo {
  isAvailable: boolean;
  canJoin: boolean;
}

export enum ClassType {
  LIVE = "Live",
  VIRTUAL = "Virtual",
  HYBRID = "Virtual & Live",
}
