import { ClassDetailsResponse } from "@/data/screens/classes/types";
import {
  ClassActionState,
  ClassAvailabilityAction,
  ClassState,
  ClassType,
  SpotAvailability,
  UserStatus,
  WaitlistInfo,
} from "./types";

const createClassState = (classDetails: ClassDetailsResponse): ClassState => ({
  isPast: classDetails.is_past ?? false,
  isCancelled: classDetails.cancelled ?? false,
  isReservationAllowed: classDetails.allow_reservations ?? true,
  isFull: classDetails.is_full ?? false,
  hasWalkinSpots: classDetails.walkin_spots_available ?? false,
});

const createUserStatus = (classDetails: ClassDetailsResponse): UserStatus => ({
  hasReservation: Boolean(classDetails.current_user_reservation) || false,
  isOnWaitlist: Boolean(classDetails.current_user_waitlist) || false,
});

const createSpotAvailability = (
  classDetails: ClassDetailsResponse
): SpotAvailability => {
  const liveSpots = classDetails.spots_available ?? 0;
  const virtualSpots = classDetails.virtual_spots_available ?? 0;

  return {
    liveSpots,
    virtualSpots,
    hasLiveSpots: liveSpots > 0,
    hasVirtualSpots: virtualSpots > 0,
  };
};

const createWaitlistInfo = (
  classDetails: ClassDetailsResponse
): WaitlistInfo => {
  const isAvailable = classDetails.is_waitlist_available ?? false;
  return {
    isAvailable,
    canJoin: isAvailable,
  };
};

const isClassFullWithWalkinsOnly = (classState: ClassState): boolean => {
  return classState.isFull && classState.hasWalkinSpots;
};

const isClassFullWithWaitlist = (
  classState: ClassState,
  waitlist: WaitlistInfo
): boolean => {
  return (
    classState.isFull && waitlist.isAvailable && !classState.hasWalkinSpots
  );
};

// Class type specific logic
const handleLiveClass = (
  spots: SpotAvailability,
  classState: ClassState,
  waitlist: WaitlistInfo
): ClassActionState => {
  if (spots.hasLiveSpots) {
    return ClassActionState.CAN_RESERVE_LIVE;
  }

  if (isClassFullWithWaitlist(classState, waitlist)) {
    return ClassActionState.CAN_JOIN_WAITLIST;
  }

  return ClassActionState.FULL_NO_OPTIONS;
};

const handleVirtualClass = (
  spots: SpotAvailability,
  classState: ClassState,
  waitlist: WaitlistInfo
): ClassActionState => {
  if (spots.hasVirtualSpots) {
    return ClassActionState.CAN_RESERVE_VIRTUAL;
  }

  if (isClassFullWithWaitlist(classState, waitlist)) {
    return ClassActionState.CAN_JOIN_WAITLIST;
  }

  return ClassActionState.FULL_NO_OPTIONS;
};

const handleHybridClass = (
  spots: SpotAvailability,
  classState: ClassState,
  waitlist: WaitlistInfo
): ClassActionState => {
  if (spots.hasLiveSpots || spots.hasVirtualSpots) {
    return ClassActionState.CAN_RESERVE_BOTH;
  }

  if (isClassFullWithWaitlist(classState, waitlist)) {
    return ClassActionState.CAN_JOIN_WAITLIST;
  }

  return ClassActionState.FULL_NO_OPTIONS;
};

export const getClassAvailabilityAction = (
  classDetails: ClassDetailsResponse
): ClassAvailabilityAction | undefined => {
  const classState = createClassState(classDetails);
  const userStatus = createUserStatus(classDetails);
  const spots = createSpotAvailability(classDetails);
  const waitlist = createWaitlistInfo(classDetails);
  const classType = classDetails.class_type;

  // Helper function to create response with metadata
  const createResponse = (
    actionState: ClassActionState
  ): ClassAvailabilityAction => ({
    actionState,
    metadata: {
      liveSpots: spots.liveSpots,
      virtualSpots: spots.virtualSpots,
      hasWaitlist: waitlist.isAvailable,
    },
  });

  // Early returns for user already engaged with class
  if (userStatus.hasReservation) {
    return createResponse(ClassActionState.USER_HAS_RESERVATION);
  }

  if (userStatus.isOnWaitlist) {
    return createResponse(ClassActionState.USER_ON_WAITLIST);
  }

  // Early returns for unavailable classes
  if (classState.isPast) {
    return createResponse(ClassActionState.CLASS_PAST);
  }

  if (classState.isCancelled) {
    return createResponse(ClassActionState.CLASS_CANCELLED);
  }

  if (!classState.isReservationAllowed) {
    return createResponse(ClassActionState.RESERVATIONS_DISABLED);
  }

  // Early return if class is full with walk-ins only
  if (isClassFullWithWalkinsOnly(classState)) {
    return createResponse(ClassActionState.FULL_WITH_WALKINS);
  }

  // Handle different class types
  const actionState = (() => {
    switch (classType) {
      case ClassType.LIVE:
        return handleLiveClass(spots, classState, waitlist);
      case ClassType.VIRTUAL:
        return handleVirtualClass(spots, classState, waitlist);
      case ClassType.HYBRID:
        return handleHybridClass(spots, classState, waitlist);
      default:
        return ClassActionState.NO_ACTION_AVAILABLE;
    }
  })();

  return createResponse(actionState);
};

export const canUserTakeAction = (actionState: ClassActionState): boolean => {
  return ![
    ClassActionState.USER_HAS_RESERVATION,
    ClassActionState.USER_ON_WAITLIST,
    ClassActionState.CLASS_PAST,
    ClassActionState.CLASS_CANCELLED,
    ClassActionState.FULL_NO_OPTIONS,
  ].includes(actionState);
};

export const actionDisabled = (actionState: ClassActionState): boolean => {
  return [
    ClassActionState.CLASS_PAST,
    ClassActionState.CLASS_CANCELLED,
    ClassActionState.FULL_NO_OPTIONS,
    ClassActionState.FULL_WITH_WALKINS,
  ].includes(actionState);
};

export const getButtonText = (actionState: ClassActionState): string => {
  switch (actionState) {
    case ClassActionState.CAN_RESERVE_LIVE:
    case ClassActionState.CAN_RESERVE_VIRTUAL:
    case ClassActionState.CAN_RESERVE_BOTH:
      return "Reserve";
    case ClassActionState.CAN_JOIN_WAITLIST:
      return "Join waitlist";
    case ClassActionState.CLASS_CANCELLED:
      return "Class cancelled";
    case ClassActionState.USER_HAS_RESERVATION:
      return "Cancel reservation";
    case ClassActionState.FULL_WITH_WALKINS:
      return "Walk-in available";
    case ClassActionState.FULL_NO_OPTIONS:
      return "Class full";
    default:
      return "Unavailable";
  }
};

export const getButtonClass = (actionState: ClassActionState): string => {
  switch (actionState) {
    case ClassActionState.USER_HAS_RESERVATION:
      return "bg-rose-100 border-rose-200 text-rose-800";
    case ClassActionState.USER_ON_WAITLIST:
      return "bg-[#E6F9FC] border-[#00BFE0]";
    case ClassActionState.CAN_RESERVE_LIVE:
    case ClassActionState.CAN_RESERVE_VIRTUAL:
    case ClassActionState.CAN_RESERVE_BOTH:
      return "bg-cyan-50 border-cyan-300 text-cyan-800";
    case ClassActionState.CAN_JOIN_WAITLIST:
      return "bg-emerald-50 border-green-300 text-green-700";
    case ClassActionState.FULL_WITH_WALKINS:
      return "bg-white border-gray-500 text-black";
    case ClassActionState.FULL_WITH_WAITLIST:
      return "bg-[#E6F9FC] border-[#00BFE0]";
    case ClassActionState.CLASS_PAST:
      return "bg-[#E6F9FC] border-[#00BFE0]";
    case ClassActionState.CLASS_CANCELLED:
    case ClassActionState.FULL_NO_OPTIONS:
      return "bg-gray-200 border-gray-300 text-slate-500";
    case ClassActionState.RESERVATIONS_DISABLED:
      return "bg-[#E6F9FC] border-[#00BFE0]";
    case ClassActionState.NO_ACTION_AVAILABLE:
      return "bg-[#E6F9FC] border-[#00BFE0]";
    default:
      return "bg-[#E6F9FC] border-[#00BFE0]";
  }
};
