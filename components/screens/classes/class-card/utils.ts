import { ClassDetailsResponse } from "@/data/screens/classes/types";

export const getButtonStatus = (classData: ClassDetailsResponse) => {
  // Check if class is cancelled
  if (classData.cancelled) return "class_cancelled";

  // Check if user has a reservation
  if (classData.current_user_reservation) return "cancel_reservation";

  // Check if user is on waitlist
  if (classData.current_user_waitlist) return "waitlist";

  // Check if class is full but allows waitlist
  if (classData.is_full && classData.allow_waitlist) return "waitlist";

  // Check if class is full but has walk-in spots
  if (classData.is_full && classData.walkin_spots_available)
    return "walk_in_available";

  // Check if class is full
  if (classData.is_full) return "class_full";

  // Default to available
  return "available";
};
