import React, { useState } from "react";
import { TouchableOpacity } from "react-native";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Box } from "@/components/ui/box";
import { Heart, ChevronDown, ChevronUp } from "lucide-react-native";
import { Icon } from "@/components/ui/icon";
import { Image } from "@/components/ui/image";
import { truncateText } from "@/utils/common";
import {
  getInitials,
  getRandomColorForInitials,
} from "@/data/common/common.utils";
import { ActivityEquipment } from "@/data/screens/activities/types";
import { StatusButton } from "@/components/shared/status-button";
import { DurationSelector } from "@/components/shared/duration-selector";
import { TimeSlotSelector } from "@/components/shared/time-slot-selector";
import { useForm } from "@tanstack/react-form";
import {
  useReserveActivity,
  useCancelActivityReservation,
} from "@/data/screens/activities/mutations/useReserveActivity";
import {
  StatusActionSheet,
  ActionButton,
} from "@/components/shared/status-actionsheet";

interface ActivityEquipmentCardProps {
  equipment: ActivityEquipment;
  selectedDate: Date;
  onFavoritePress?: () => void;
}

const getStatusConfig = (equipment: ActivityEquipment) => {
  switch (equipment.status) {
    case "available":
      return {
        variant: "reserve" as const,
        text: "Reserve",
        disabled: false,
        statusBadge: null,
      };
    case "reserved":
      return {
        variant: "cancel_reservation" as const,
        text: "Cancel reservation",
        disabled: false,
        statusBadge: {
          text: "Reserved",
          classes: "bg-green-100 text-green-800",
        },
      };
    case "full":
      return {
        variant: "event_full" as const,
        text: "Full",
        disabled: true,
        statusBadge: {
          text: "Full",
          classes: "bg-gray-200 text-gray-600",
        },
      };
    case "maintenance":
      return {
        variant: "event_cancelled" as const,
        text: "Maintenance",
        disabled: true,
        statusBadge: null,
      };
    default:
      return {
        variant: "reserve" as const,
        text: "Reserve",
        disabled: false,
        statusBadge: null,
      };
  }
};

const getActivityActions = (equipment: ActivityEquipment): ActionButton[] => [
  {
    label: "Make another reservation",
    onPress: () => {},
    variant: "primary",
  },
  {
    label: "Add to calendar",
    onPress: () => {},
    variant: "secondary",
  },
  {
    label: "Share with friends",
    onPress: () => {},
    variant: "outline",
  },
];

export const ActivityEquipmentCard = ({
  equipment,
  selectedDate,
  onFavoritePress,
}: ActivityEquipmentCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showActionSheet, setShowActionSheet] = useState(false);
  const [actionSheetError, setActionSheetError] = useState<Error | null>(null);

  const { mutate: reserveActivity, isPending: isReserving } =
    useReserveActivity();
  const { mutate: cancelReservation, isPending: isCancelling } =
    useCancelActivityReservation();

  const statusConfig = getStatusConfig(equipment);
  const isPending = isReserving || isCancelling;

  const form = useForm({
    defaultValues: {
      duration: "",
      startTime: "",
    },
    onSubmit: async ({ value }) => {
      if (!value.duration || !value.startTime) return;

      reserveActivity(
        {
          equipment_id: equipment.id,
          start_time: value.startTime,
          duration: parseInt(value.duration),
          facility_id: equipment.facility_id,
        },
        {
          onSuccess: () => {
            setActionSheetError(null);
            setShowActionSheet(true);
            setIsExpanded(false);
            form.reset();
          },
          onError: (error) => {
            setActionSheetError(error);
            setShowActionSheet(true);
          },
        }
      );
    },
  });

  const handleReserve = () => {
    if (equipment.status === "available") {
      setIsExpanded(!isExpanded);
    } else if (equipment.status === "reserved") {
      // Handle cancel reservation - assuming we have reservation ID
      // This would need to be passed from the equipment data
      cancelReservation(equipment.id, {
        onSuccess: () => {
          setActionSheetError(null);
          setShowActionSheet(true);
        },
        onError: (error) => {
          setActionSheetError(error);
          setShowActionSheet(true);
        },
      });
    }
  };

  return (
    <VStack
      space="sm"
      className="bg-white rounded-2xl p-4 border border-background-200 mb-2"
    >
      <HStack space="sm" className="items-start">
        {equipment.image_url ? (
          <Image
            source={{ uri: equipment.image_url }}
            className="w-12 h-12 rounded-lg"
            alt={equipment.name}
          />
        ) : (
          <Box
            className="w-12 h-12 rounded-lg items-center justify-center"
            style={{
              backgroundColor: getRandomColorForInitials(equipment.name),
            }}
          >
            <Text className="text-sm font-dm-sans-bold text-white">
              {getInitials(equipment.name)}
            </Text>
          </Box>
        )}

        <VStack className="flex-1" space="xs">
          <HStack className="items-center justify-between">
            <HStack className="items-center flex-1" space="sm">
              <Text
                className="text-[#00697B] font-dm-sans-bold text-base"
                numberOfLines={2}
              >
                {truncateText(equipment.name, 20)}
              </Text>
              {statusConfig.statusBadge && (
                <Box
                  className={`px-2 py-1 rounded-md ${statusConfig.statusBadge.classes}`}
                >
                  <Text
                    className={`text-xs font-dm-sans-medium ${statusConfig.statusBadge.classes}`}
                  >
                    {statusConfig.statusBadge.text}
                  </Text>
                </Box>
              )}
            </HStack>
            <TouchableOpacity
              className="ml-2"
              onPress={(e) => {
                e.stopPropagation();
                onFavoritePress?.();
              }}
            >
              <Icon
                as={Heart}
                size="sm"
                className={
                  equipment.is_favorite
                    ? "text-error-500 fill-error-500"
                    : "text-typography-400"
                }
              />
            </TouchableOpacity>
          </HStack>

          <HStack className="items-center" space="xs">
            {equipment.next_available_time &&
              equipment.status !== "available" && (
                <Text className="text-sm font-dm-sans-regular text-[#00BFE0]">
                  {equipment.status === "reserved"
                    ? "Available"
                    : "Next available"}
                  : {equipment.next_available_time}
                </Text>
              )}
          </HStack>
        </VStack>
      </HStack>

      <HStack className="justify-between items-center">
        <VStack space="xs">
          <Text className="text-xs font-dm-sans-regular text-typography-600">
            {truncateText(equipment.facility_name, 25)}
          </Text>
        </VStack>

        <HStack space="sm" className="items-center">
          <StatusButton
            variant={statusConfig.variant}
            text={statusConfig.text}
            disabled={statusConfig.disabled}
            isLoading={isPending}
            size="sm"
            onPress={handleReserve}
          />
          {equipment.status === "available" && (
            <TouchableOpacity onPress={() => setIsExpanded(!isExpanded)}>
              <Icon
                as={isExpanded ? ChevronUp : ChevronDown}
                size="sm"
                className="text-typography-600"
              />
            </TouchableOpacity>
          )}
        </HStack>
      </HStack>

      {isExpanded && equipment.status === "available" && (
        <form.Provider>
          <VStack
            space="md"
            className="mt-4 pt-4 border-t border-background-200"
          >
            <form.Field
              name="startTime"
              children={(field) => (
                <TimeSlotSelector
                  value={field.state.value}
                  onChange={field.handleChange}
                  placeholder="02:00PM"
                />
              )}
            />

            <form.Field
              name="duration"
              children={(field) => (
                <DurationSelector
                  value={field.state.value}
                  onChange={field.handleChange}
                  durations={equipment.duration_options}
                  placeholder="30 minutes"
                />
              )}
            />

            <form.Subscribe
              selector={(state) => [
                state.values.duration,
                state.values.startTime,
              ]}
              children={([duration, startTime]) => (
                <StatusButton
                  variant="reserve"
                  text="Reserve"
                  disabled={!duration || !startTime}
                  isLoading={isPending}
                  size="sm"
                  onPress={form.handleSubmit}
                />
              )}
            />
          </VStack>
        </form.Provider>
      )}

      <StatusActionSheet
        isOpen={showActionSheet}
        onClose={() => setShowActionSheet(false)}
        status={actionSheetError ? "error" : "success"}
        title={actionSheetError ? "Error occurred" : "Reservation made"}
        description={
          actionSheetError?.message ??
          "Your activity reservation was successful!"
        }
        actions={actionSheetError ? [] : getActivityActions(equipment)}
      />
    </VStack>
  );
};
