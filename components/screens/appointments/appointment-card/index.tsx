import React from "react";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";

import { Button, ButtonText } from "@/components/ui/button";

import { AppointmentType } from "@/data/screens/appointments/types";
import { Box } from "@/components/ui/box";
import {
  getInitials,
  getRandomColorForInitials,
} from "@/data/common/common.utils";
import { router } from "expo-router";
import { truncateText } from "@/utils/common";

export const AppointmentCard: React.FC<AppointmentType> = ({ id, name }) => {
  const backgroundColor = getRandomColorForInitials(name);

  return (
    <VStack
      space="sm"
      className="bg-white rounded-2xl p-2 border border-background-200 mb-2"
    >
      <HStack space="md">
        <Box
          className="w-16 h-16 rounded-lg items-center justify-center"
          style={{ backgroundColor }}
        >
          <Text className="text-sm font-dm-sans-bold text-white">
            {getInitials(name)}
          </Text>
        </Box>

        <VStack className="flex-1" space="xs">
          <Text className="font-dm-sans-bold text-base">
            {truncateText(name, 20)}
          </Text>
          <HStack space="sm">
            <Button
              variant="outline"
              size="xs"
              className="flex-1 rounded-full border-background-300"
              onPress={() =>
                router.push({
                  pathname: "/trainers",
                  params: { sessionId: id },
                })
              }
            >
              <ButtonText className="text-typography-700 font-dm-sans-medium">
                Trainers
              </ButtonText>
            </Button>
            <Button
              variant="outline"
              size="xs"
              className="flex-1 rounded-full border-background-300"
            >
              <ButtonText className="text-typography-700 font-dm-sans-medium">
                Purchase
              </ButtonText>
            </Button>

            <Button
              variant="solid"
              size="xs"
              className="flex-1 rounded-full bg-[#E6F9FC]"
            >
              <ButtonText className="text-[#00697B] font-dm-sans-medium font-bold">
                Schedule
              </ButtonText>
            </Button>
          </HStack>
        </VStack>
      </HStack>
    </VStack>
  );
};
