import { create } from 'zustand';
import { ClassDetailsResponse } from '@/data/screens/classes/types';
import { AppointmentType } from '@/data/screens/appointments/types';

interface ClassesState {
  // Classes data
  classes: ClassDetailsResponse[];
  appointments: AppointmentType[];
  
  // UI state
  selectedTab: 'classes' | 'appointment';
  selectedDate: Date;
  searchTerm: string;
  
  // Loading states
  classesLoading: boolean;
  appointmentsLoading: boolean;
  
  // Error states
  classesError: Error | null;
  appointmentsError: Error | null;
  
  // Actions
  setClasses: (classes: ClassDetailsResponse[]) => void;
  setAppointments: (appointments: AppointmentType[]) => void;
  setSelectedTab: (tab: 'classes' | 'appointment') => void;
  setSelectedDate: (date: Date) => void;
  setSearchTerm: (term: string) => void;
  setClassesLoading: (loading: boolean) => void;
  setAppointmentsLoading: (loading: boolean) => void;
  setClassesError: (error: Error | null) => void;
  setAppointmentsError: (error: Error | null) => void;
  clearSearch: () => void;
  reset: () => void;
}

export const useClassesStore = create<ClassesState>((set) => ({
  // Initial state
  classes: [],
  appointments: [],
  selectedTab: 'classes',
  selectedDate: new Date(),
  searchTerm: '',
  classesLoading: false,
  appointmentsLoading: false,
  classesError: null,
  appointmentsError: null,

  // Actions
  setClasses: (classes) => set({ 
    classes, 
    classesError: null 
  }),

  setAppointments: (appointments) => set({ 
    appointments, 
    appointmentsError: null 
  }),

  setSelectedTab: (selectedTab) => set({ selectedTab }),

  setSelectedDate: (selectedDate) => set({ selectedDate }),

  setSearchTerm: (searchTerm) => set({ searchTerm }),

  setClassesLoading: (classesLoading) => set({ classesLoading }),

  setAppointmentsLoading: (appointmentsLoading) => set({ appointmentsLoading }),

  setClassesError: (classesError) => set({ 
    classesError, 
    classesLoading: false 
  }),

  setAppointmentsError: (appointmentsError) => set({ 
    appointmentsError, 
    appointmentsLoading: false 
  }),

  clearSearch: () => set({ searchTerm: '' }),

  reset: () => set({
    classes: [],
    appointments: [],
    selectedTab: 'classes',
    selectedDate: new Date(),
    searchTerm: '',
    classesLoading: false,
    appointmentsLoading: false,
    classesError: null,
    appointmentsError: null,
  }),
}));
