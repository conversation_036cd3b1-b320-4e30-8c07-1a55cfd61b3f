import * as SecureStore from "expo-secure-store";

// Keys for storing biometric authentication data
export const BIOMETRIC_ENABLED_KEY = "__biometric_enabled";
export const BIOMETRIC_CREDENTIALS_KEY = "__biometric_credentials";

export interface BiometricCredentials {
  email: string;
  password: string; // Store encrypted password for API authentication
}

/**
 * Check if biometric authentication is enabled for the user
 */
export const isBiometricEnabled = async (): Promise<boolean> => {
  try {
    const enabled = await SecureStore.getItemAsync(BIOMETRIC_ENABLED_KEY);
    const isEnabled = enabled === "true";
    console.log("Checking biometric enabled status:", { enabled, isEnabled });
    return isEnabled;
  } catch (error) {
    console.error("Error checking biometric enabled status:", error);
    return false;
  }
};

/**
 * Enable or disable biometric authentication
 */
export const setBiometricEnabled = async (enabled: boolean): Promise<void> => {
  try {
    if (enabled) {
      await SecureStore.setItemAsync(BIOMETRIC_ENABLED_KEY, "true");
    } else {
      await SecureStore.deleteItemAsync(BIOMETRIC_ENABLED_KEY);
      // Also clear stored credentials when disabling
      await clearBiometricCredentials();
    }
  } catch (error) {
    console.error("Error setting biometric enabled status:", error);
    throw new Error("Failed to update biometric settings");
  }
};

/**
 * Store user credentials for biometric authentication
 */
export const storeBiometricCredentials = async (
  credentials: BiometricCredentials
): Promise<void> => {
  try {
    await SecureStore.setItemAsync(
      BIOMETRIC_CREDENTIALS_KEY,
      JSON.stringify(credentials)
    );
  } catch (error) {
    console.error("Error storing biometric credentials:", error);
    throw new Error("Failed to store biometric credentials");
  }
};

/**
 * Retrieve stored biometric credentials
 */
export const getBiometricCredentials =
  async (): Promise<BiometricCredentials | null> => {
    try {
      const credentialsString = await SecureStore.getItemAsync(
        BIOMETRIC_CREDENTIALS_KEY
      );
      if (credentialsString) {
        const credentials = JSON.parse(credentialsString);
        console.log("Retrieved biometric credentials:", {
          hasEmail: !!credentials.email,
          hasPassword: !!credentials.password,
        });
        return credentials;
      }
      console.log("No biometric credentials found");
      return null;
    } catch (error) {
      console.error("Error retrieving biometric credentials:", error);
      return null;
    }
  };

/**
 * Clear stored biometric credentials
 */
export const clearBiometricCredentials = async (): Promise<void> => {
  try {
    await SecureStore.deleteItemAsync(BIOMETRIC_CREDENTIALS_KEY);
  } catch (error) {
    console.error("Error clearing biometric credentials:", error);
  }
};

/**
 * Simple encryption for password storage (in production, use proper encryption)
 * Note: Since we're using SecureStore, the data is already encrypted at rest
 */
export const encryptPassword = (password: string): string => {
  // For demo purposes, we'll just store the password as-is since SecureStore handles encryption
  // In production, you might want additional encryption layers
  return password;
};

export const decryptPassword = (encryptedPassword: string): string => {
  // For demo purposes, just return the password as-is
  return encryptedPassword;
};
