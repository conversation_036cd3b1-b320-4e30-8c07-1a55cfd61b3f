import { Alert, Linking } from "react-native";
import { format, parseISO } from "date-fns";

interface ClassEvent {
  title: string;
  startTime: string;
  endTime: string;
  date: string;
  location?: string;
  description?: string;
  instructor?: string;
}

/**
 * Utility function to add a class event to the device calendar
 * @param classEvent The class event details
 */
export const addToCalendar = async (classEvent: ClassEvent) => {
  try {
    // Format the date and time for calendar URL
    const startDate = new Date(`${classEvent.date}T${classEvent.startTime}`);
    const endDate = new Date(`${classEvent.date}T${classEvent.endTime}`);
    
    // Format dates for calendar URL (YYYYMMDDTHHMMSSZ format)
    const formatForCalendar = (date: Date) => {
      return format(date, "yyyyMMdd'T'HHmmss'Z'");
    };

    const startFormatted = formatForCalendar(startDate);
    const endFormatted = formatForCalendar(endDate);

    // Create calendar event details
    const title = encodeURIComponent(classEvent.title);
    const details = encodeURIComponent(
      `${classEvent.description || "Fitness class"}\n\nInstructor: ${classEvent.instructor || "TBD"}`
    );
    const location = encodeURIComponent(classEvent.location || "");

    // Create Google Calendar URL (works on most devices)
    const googleCalendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${title}&dates=${startFormatted}/${endFormatted}&details=${details}&location=${location}`;

    // Try to open the calendar URL
    const canOpen = await Linking.canOpenURL(googleCalendarUrl);
    
    if (canOpen) {
      await Linking.openURL(googleCalendarUrl);
    } else {
      // Fallback: Show alert with event details
      Alert.alert(
        "Add to Calendar",
        `Event: ${classEvent.title}\nDate: ${format(startDate, "MMM dd, yyyy")}\nTime: ${format(startDate, "h:mm a")} - ${format(endDate, "h:mm a")}\n\nPlease manually add this event to your calendar.`,
        [{ text: "OK" }]
      );
    }
  } catch (error) {
    console.error("Error adding to calendar:", error);
    Alert.alert(
      "Calendar Error",
      "Unable to add event to calendar. Please try again or add manually.",
      [{ text: "OK" }]
    );
  }
};

/**
 * Create a shareable text for a class reservation
 * @param classEvent The class event details
 * @returns Formatted share text
 */
export const createShareText = (classEvent: ClassEvent): string => {
  const startDate = new Date(`${classEvent.date}T${classEvent.startTime}`);
  const endDate = new Date(`${classEvent.date}T${classEvent.endTime}`);
  
  return `🏋️‍♀️ I just booked a class!

📅 ${classEvent.title}
🗓️ ${format(startDate, "EEEE, MMM dd, yyyy")}
⏰ ${format(startDate, "h:mm a")} - ${format(endDate, "h:mm a")}
👨‍🏫 Instructor: ${classEvent.instructor || "TBD"}
📍 ${classEvent.location || ""}

Join me for a great workout! 💪`;
};
