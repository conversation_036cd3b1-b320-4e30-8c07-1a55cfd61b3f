import React, { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { Icon } from "@/components/ui/icon";
import { Input, InputField, InputIcon, InputSlot } from "@/components/ui/input";
import { ScrollView } from "react-native";
import { router } from "expo-router";
import {
  Search,
  Check,
  X,
} from "lucide-react-native";

interface LocationItemProps {
  name: string;
  isSelected?: boolean;
  onPress?: () => void;
}

const LocationItem: React.FC<LocationItemProps> = ({ name, isSelected = false, onPress }) => {
  return (
    <Pressable
      onPress={onPress}
      className="flex-row items-center justify-between px-4 py-3 active:bg-background-50"
    >
      <Text className="text-base font-dm-sans-regular text-typography-700 flex-1">
        {name}
      </Text>
      {isSelected && (
        <Icon
          as={Check}
          size="md"
          className="text-primary-500"
        />
      )}
    </Pressable>
  );
};

const Locations = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedLocation, setSelectedLocation] = useState("Collins & Katz Family YMCA");

  const handleClose = () => {
    router.back();
  };

  const handleSearch = (text: string) => {
    setSearchTerm(text);
  };

  const handleLocationSelect = (location: string) => {
    setSelectedLocation(location);
    // TODO: Save selected location and navigate back
    setTimeout(() => {
      router.back();
    }, 300);
  };

  // Locations based on the design
  const locations = [
    "All locations",
    "Anderson Munger Family YMCA",
    "Ann & Steve Hinchliffe San Pedro & Peninsula YMCA",
    "Antelope Valley Family YMCA",
    "Burbank Family YMCA",
    "Collins & Katz Family YMCA",
    "Crenshaw Family YMCA",
    "Culver-Palms Family YMCA",
    "East Valley Family YMCA",
  ];

  const filteredLocations = locations.filter(location =>
    location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1">
        {/* Header */}
        <HStack className="px-4 py-4 items-center justify-between border-b border-background-100">
          <Text className="text-lg font-dm-sans-bold text-typography-900">
            Locations
          </Text>
          <Pressable onPress={handleClose} className="p-1">
            <Icon as={X} size="lg" className="text-typography-600" />
          </Pressable>
        </HStack>

        {/* Search */}
        <VStack className="px-4 py-4 border-b border-background-100">
          <Input variant="outline" className="bg-background-50 rounded-xl" size="lg">
            <InputSlot>
              <InputIcon as={Search} className="text-typography-400 ml-3" />
            </InputSlot>
            <InputField
              placeholder="Search location"
              className="placeholder:text-typography-400"
              onChangeText={handleSearch}
              value={searchTerm}
            />
          </Input>
        </VStack>

        <ScrollView 
          className="flex-1" 
          showsVerticalScrollIndicator={false}
        >
          {/* Locations List */}
          <VStack>
            {filteredLocations.map((location, index) => (
              <VStack key={location}>
                <LocationItem
                  name={location}
                  isSelected={location === selectedLocation}
                  onPress={() => handleLocationSelect(location)}
                />
                {index < filteredLocations.length - 1 && (
                  <VStack className="h-px bg-background-100 mx-4" />
                )}
              </VStack>
            ))}
          </VStack>

          {filteredLocations.length === 0 && searchTerm && (
            <VStack className="items-center justify-center py-8">
              <Text className="text-typography-500 font-dm-sans-regular">
                No locations found matching "{searchTerm}"
              </Text>
            </VStack>
          )}
        </ScrollView>
      </VStack>
    </SafeAreaView>
  );
};

export default Locations;
