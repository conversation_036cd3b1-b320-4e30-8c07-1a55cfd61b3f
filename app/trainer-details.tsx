import React from "react";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import { SafeAreaView } from "react-native-safe-area-context";
import { ImageBackground, ScrollView, StatusBar, View } from "react-native";
import { Pressable } from "@/components/ui/pressable";
import { Icon } from "@/components/ui/icon";
import { ArrowLeft } from "lucide-react-native";
import { router, useLocalSearchParams } from "expo-router";

import { Box } from "@/components/ui/box";

import { useUserInfo } from "@/data/screens/common/queries/useUserInfo";
import { TrainerDetailsSkeleton } from "@/components/screens/trainer-details/trainer-details-skeleton";
import { COLOR_CODES } from "@/constants/color-codes";

const TrainerDetails = () => {
  const { trainerId, hideButton } = useLocalSearchParams<{
    trainerId: string;
    hideButton: string;
  }>();

  const { data, isLoading, error } = useUserInfo(trainerId);

  const trainer = data?.user;

  if (isLoading) {
    return <TrainerDetailsSkeleton />;
  }

  if (error || !trainer) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <VStack className="flex-1 items-center justify-center px-4">
          <Text className="text-lg font-dm-sans-bold text-typography-900">
            Trainer not found
          </Text>
        </VStack>
      </SafeAreaView>
    );
  }

  const statusBarHeight = StatusBar.currentHeight || 44;

  const initials = `${trainer.first_name?.[0] ?? ""}${
    trainer.last_name?.[0] ?? ""
  }`.toUpperCase();

  return (
    <View className="flex-1 bg-background-0">
      <View className="h-[370px] w-full">
        <StatusBar barStyle="light-content" />
        {trainer.profile_image ? (
          <ImageBackground
            source={{
              uri: trainer.profile_image,
            }}
            alt={`${trainer.first_name} profile-picture`}
            className="h-full w-full justify-center"
            imageStyle={{
              opacity: 0.8,
              backgroundColor: COLOR_CODES.trainer.background,
            }}
          >
            <Pressable
              onPress={() => router.back()}
              style={{
                position: "absolute",
                top: statusBarHeight + 16,
                left: 16,
                zIndex: 10,
              }}
              className="p-2 bg-slate-500 rounded-full"
            >
              <Icon
                as={ArrowLeft}
                size="sm"
                color="white"
                className="text-typography-900"
              />
            </Pressable>
          </ImageBackground>
        ) : (
          <VStack
            style={{ backgroundColor: COLOR_CODES.trainer.background }}
            className="h-full w-full items-center justify-center flex"
          >
            <Pressable
              onPress={() => router.back()}
              style={{
                position: "absolute",
                top: statusBarHeight + 16,
                left: 16,
                zIndex: 10,
              }}
              className="p-2 bg-black/20 rounded-full"
            >
              <Icon
                as={ArrowLeft}
                size="sm"
                color="white"
                className="text-typography-900"
              />
            </Pressable>
            <VStack
              className={`w-32 h-32 rounded-full bg-[#8AE2F1] items-center justify-center  border-background-warning-500 p-2`}
            >
              <Text
                className={`text-[${COLOR_CODES.trainer.text}] text-5xl font-dm-sans-bold text-center mt-2`}
              >
                {initials}
              </Text>
            </VStack>
          </VStack>
        )}
      </View>

      <Box className="bg-white rounded-t-3xl -mt-2 flex-1">
        <ScrollView
          contentContainerStyle={{ paddingBottom: 120 }}
          showsVerticalScrollIndicator={false}
        >
          <VStack className="p-6" space="lg">
            <VStack space="xs">
              <Text className="text-[#00697B] font-dm-sans-bold text-3xl">
                {trainer.first_name} {trainer.last_name}
              </Text>
              <Text className="text-typography-600 font-dm-sans-regular text-base">
                {trainer.years_experience} years of experience
              </Text>
            </VStack>

            <VStack space="sm">
              <Text className="text-typography-900 font-dm-sans-bold text-base">
                Expertise
              </Text>
              <HStack space="sm" className="flex-wrap">
                {trainer.specialties.map((specialty, index) => (
                  <View
                    key={index}
                    className="border border-background-200 rounded-lg px-3 py-1.5 flex-row items-center"
                  >
                    <View className="w-2 h-2 rounded-full bg-[#00BFE0] mr-2" />
                    <Text className="text-typography-700 text-sm font-dm-sans-regular">
                      {specialty.name}
                    </Text>
                  </View>
                ))}
              </HStack>
            </VStack>

            <VStack space="sm">
              <Text className="text-typography-900 font-dm-sans-bold text-base">
                Description
              </Text>
              <Text className="text-typography-600 font-dm-sans-regular text-base leading-6">
                {trainer.bio}
              </Text>
            </VStack>
          </VStack>
        </ScrollView>
      </Box>

      {/* Fixed Bottom Button */}
      {!Boolean(hideButton) && (
        <View className="absolute bottom-0 left-0 right-0 bg-white">
          <VStack className="px-6 py-4 border-t border-background-200">
            <Button
              variant="solid"
              size="lg"
              className="rounded-full bg-[#E0F7FA] h-14"
            >
              <ButtonText className="text-[#00697B] font-dm-sans-bold text-base">
                Book
              </ButtonText>
            </Button>
          </VStack>
        </View>
      )}
    </View>
  );
};

export default TrainerDetails;
