import * as React from "react";
import { View, Text } from "react-native";
import { Button, ButtonText } from "@/components/ui/button";
import { router } from "expo-router";
import { ArrowRight, Check } from "lucide-react-native";

export default function ResetSuccess() {
  const handleContinue = () => {
    router.replace("/(auth)/sign-in");
  };

  return (
    <View className="flex-1 bg-background-0 justify-center items-center px-6">
      <View className="items-center">
        <View className="w-24 h-24 rounded-full bg-success-100 justify-center items-center mb-8">
          <Check className="text-success-500" size={48} />
        </View>

        <Text className="text-2xl font-dm-sans-bold text-center mb-2">
          Success!
        </Text>

        <Text className="text-gray-600 text-center mb-8">
          Your password has been reset successfully. Login with your new
          password
        </Text>

        <Button
          className="bg-[#00697B] rounded-full w-full mb-4"
          onPress={handleContinue}
        >
          <ButtonText>Continue to login</ButtonText>
          <ArrowRight className="ml-2 text-white" size={20} />
        </Button>
      </View>
    </View>
  );
}
