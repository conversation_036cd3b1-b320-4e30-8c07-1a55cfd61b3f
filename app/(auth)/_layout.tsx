import { Stack, useLocalSearchParams } from "expo-router";

export default function LoginLayout() {
  const { email } = useLocalSearchParams<{ email?: string }>();

  return (
    <Stack>
      <Stack.Screen
        name="sign-in"
        initialParams={{ email: "<EMAIL>" }}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="forgot-password"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="reset-password"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="reset-success"
        options={{
          headerShown: false,
        }}
      />
    </Stack>
  );
}
