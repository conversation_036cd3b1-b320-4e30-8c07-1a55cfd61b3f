/* eslint-disable react/no-children-prop */
/* eslint-disable @typescript-eslint/no-require-imports */
import { Box } from "@/components/ui/box";
import * as React from "react";
import { View, Text, ImageBackground } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import { Input, InputField, InputSlot, InputIcon } from "@/components/ui/input";
import { Button, ButtonText } from "@/components/ui/button";
import { Alert, AlertText, AlertIcon } from "@/components/ui/alert";
import { AlertCircle, ArrowLeft, Mail } from "lucide-react-native";
import {
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
} from "@/components/ui/form-control";
import { useState, useEffect } from "react";
import { router, useLocalSearchParams } from "expo-router";
import { useCheckEmail } from "@/modules/login/hooks/useCheckEmail";
import { Pressable } from "@/components/ui/pressable";
import { useForm } from "@tanstack/react-form";
import { forgotPasswordSchema } from "@/modules/login/schemas";
import { isEmpty } from "lodash/fp";

export default function ForgotPassword() {
  const [error, setError] = useState<string | null>(null);
  const { email: emailParam } = useLocalSearchParams<{ email?: string }>();

  const {
    mutate: checkEmail,
    isPending,
    isError,
    error: apiError,
    data,
  } = useCheckEmail((email) => {
    // On success, navigate to reset password screen with email
    router.replace({
      pathname: "/(auth)/reset-password",
      params: { email },
    });
  });

  // Handle API errors
  useEffect(() => {
    if (isError && apiError) {
      setError(
        apiError.message || "Failed to send reset email. Please try again."
      );
    } else if (data && !data.success) {
      setError(data.message || "Failed to send reset email. Please try again.");
    }
  }, [isError, apiError, data]);

  const form = useForm({
    defaultValues: {
      email: emailParam || "",
    },
    validators: {
      onChange: forgotPasswordSchema,
    },
    onSubmit: async ({ value }) => {
      setError(null);
      checkEmail(value.email);
    },
  });

  const handleBack = () => {
    router.back();
  };

  return (
    <KeyboardAwareScrollView
      bottomOffset={90}
      enabled={true}
      keyboardShouldPersistTaps="handled"
      contentContainerStyle={{
        flexGrow: 1,
      }}
    >
      <View className="flex-1 bg-background-0">
        <View className="h-[330px]">
          <ImageBackground
            source={require("@/assets/images/login-bg.png")}
            className="h-full w-full justify-center items-center"
          >
            <Text className="text-3xl font-dm-sans-bold text-white">YMCA</Text>
          </ImageBackground>
        </View>

        <Box className="bg-background-0 rounded-t-3xl -mt-5 px-6 pt-6 pb-10">
          <Pressable onPress={handleBack} className="mb-4">
            <ArrowLeft className="text-[#00697B]" />
          </Pressable>

          <Text className="text-2xl font-dm-sans-bold text-[#00697B] mb-2">
            Forgot Password
          </Text>
          <Text className="text-gray-600 mb-6">
            Enter your email address and we&rsquo;ll send you a PIN to reset
            your password
          </Text>

          {/* Error Message */}
          {error && (
            <Alert action="error" className="mb-3">
              <AlertIcon as={AlertCircle} />
              <AlertText className="text-sm">{error}</AlertText>
            </Alert>
          )}

          {/* Email Input */}
          <form.Field
            name="email"
            children={(field) => (
              <FormControl className="mb-6">
                <FormControlLabel>
                  <FormControlLabelText className="text-gray-600">
                    Email
                  </FormControlLabelText>
                </FormControlLabel>
                <Input
                  variant="outline"
                  size="lg"
                  className={`border ${
                    !isEmpty(field.state.meta.errors)
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-lg`}
                  accessibilityLabel="Email input"
                  accessibilityHint="Enter your email address to receive a PIN"
                >
                  <InputSlot className="pl-3">
                    <InputIcon as={Mail} className="text-gray-500" />
                  </InputSlot>
                  <InputField
                    placeholder="Enter your email address"
                    value={field.state.value}
                    onChangeText={field.handleChange}
                    onBlur={field.handleBlur}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    className="text-gray-800"
                    accessibilityRole="text"
                    textContentType="emailAddress"
                    returnKeyType="done"
                    onSubmitEditing={() => form.handleSubmit()}
                  />
                </Input>
                {field.state.meta.errors ? (
                  <FormControlError>
                    <FormControlErrorText>
                      {field.state.meta.errors.join(", ")}
                    </FormControlErrorText>
                  </FormControlError>
                ) : null}
              </FormControl>
            )}
          />

          <form.Subscribe
            selector={(state) => [state.canSubmit, state.isSubmitting]}
          >
            {([canSubmit, isSubmitting]) => (
              <Button
                className="bg-[#00697B] rounded-full mb-4"
                onPress={() => form.handleSubmit()}
                disabled={!canSubmit || isPending || isSubmitting}
                accessibilityLabel={isPending ? "Sending..." : "Send PIN"}
                accessibilityRole="button"
                accessibilityState={{
                  disabled: !canSubmit || isPending || isSubmitting,
                }}
              >
                <ButtonText>{isPending ? "Sending..." : "Send PIN"}</ButtonText>
              </Button>
            )}
          </form.Subscribe>

          <View className="items-center pt-2">
            <Text className="text-gray-500">
              Remember your password?{" "}
              <Text
                className="text-[#00697B] font-dm-sans-medium"
                onPress={handleBack}
              >
                Back to login
              </Text>
            </Text>
          </View>
        </Box>
      </View>
    </KeyboardAwareScrollView>
  );
}
