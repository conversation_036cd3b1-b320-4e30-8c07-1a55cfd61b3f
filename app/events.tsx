import React from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { VStack } from "@/components/ui/vstack";
import { EventsHeader } from "@/components/screens/events/events-header";

import { SearchInput } from "@/components/shared/search";
import { EventsList } from "@/components/screens/events/events-list";
import { useEvents } from "@/hooks/useEvents";
import { HorizontalDatePicker } from "@/components/shared/horizontal-date-picker";
import { FacilityFilter } from "@/components/shared/facility-filter";

export const Events = () => {
  const {
    events,
    isLoading,
    isRefetching,
    selectedDate,
    searchTerm,
    handleDateChange,
    clearSearch,
    refetch,
    setSearchTerm,
    facility,
    setFacility,
  } = useEvents();

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1">
        <EventsHeader />

        <VStack space="md" className="pb-4">
          <VStack>
            <HorizontalDatePicker
              selectedDate={selectedDate}
              onDateSelect={handleDateChange}
              config={{
                mode: "months",
              }}
            />
            <SearchInput
              onSearch={setSearchTerm}
              searchTerm={searchTerm}
              placeholder="Search events"
            />
            <FacilityFilter value={facility} onChange={setFacility} />
          </VStack>

          <VStack
            space="sm"
            className={events?.length ? "px-4 bg-gray-100" : ""}
          >
            <EventsList
              events={events}
              isLoading={isLoading}
              isRefreshing={isRefetching}
              searchTerm={searchTerm}
              selectedDate={selectedDate}
              onRefresh={refetch}
              onClearSearch={clearSearch}
            />
          </VStack>
        </VStack>
      </VStack>
    </SafeAreaView>
  );
};

export default Events;
