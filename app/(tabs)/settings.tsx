import React from "react";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";

import { useSession } from "@/modules/login/auth-provider";
import BiometricSettings from "@/components/screens/settings/biometric-settings";
import { BiometricTest } from "@/components/test/biometric-test";

import { Button, ButtonText } from "@/components/ui/button";
import { OverlayProvider } from "@gluestack-ui/overlay";

function Example() {
  return (
    <Button>
      <ButtonText>Press Me</ButtonText>
    </Button>
  );
}

const Settings = () => {
  const { signOut } = useSession();

  return (
    <VStack space="md" className="bg-background-0 mt-12">
      {/* Biometric Settings */}
      <BiometricSettings />

      <VStack className="px-4" space="md">
        <OverlayProvider>
          {/* Biometric Test Component */}
          <BiometricTest />

          {/* Toast Test Components */}

          {/* <ReservationErrorTest /> */}
          {/* <AppointmentTest /> */}

          <Text className="font-semibold">Logout</Text>
          <VStack className="px-4" space="md">
            <Button
              onPress={signOut}
              variant="solid"
              className="w-full bg-[#00697B] rounded-full"
            >
              <Text className="text-white font-dm-sans-medium text-center">
                Logout
              </Text>
            </Button>
          </VStack>
          <Example />
        </OverlayProvider>
      </VStack>
    </VStack>
  );
};

export default Settings;
