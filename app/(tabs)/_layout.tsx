import React from "react";
import { Tabs } from "expo-router";
import { BottomTabBar } from "@/components/shared/bottom-tab-bar";
import { BottomTabBarProps } from "@react-navigation/bottom-tabs";

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
      }}
      tabBar={(props: BottomTabBarProps) => <BottomTabBar {...props} />}
    />
  );
}
