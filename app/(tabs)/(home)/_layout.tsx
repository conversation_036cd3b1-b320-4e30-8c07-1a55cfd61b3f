/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useContext, useEffect } from "react";

import { View } from "@/components/ui/view";

import {
  ClassTabProvider,
  ClassTabContext,
} from "@/contexts/class-screen-context";
import { useSharedValue } from "react-native-reanimated";
import { Text } from "@/components/ui/text";

const ClassLayout = () => {
  const { scrollViewRef, selectedTabIndex }: any = useContext(ClassTabContext);

  const scrollY = useSharedValue(0);

  const animatedHeight = useSharedValue(340);
  const isHeaderShrunk = useSharedValue(false);

  useEffect(() => {
    if (scrollViewRef.current) {
      const targetY = isHeaderShrunk.value ? 200 : 0;

      scrollViewRef.current.scrollTo({
        y: targetY,
        animated: false,
      });

      scrollY.value = targetY;
      animatedHeight.value = isHeaderShrunk.value ? 140 : 340;
    }
  }, [
    selectedTabIndex,
    scrollViewRef,
    isHeaderShrunk,
    scrollY,
    animatedHeight,
  ]);

  return (
    <View className="flex-1 items-center justify-center">
      <Text className="text-2xl font-dm-sans-bold">Welcome to UPACE</Text>
    </View>
  );
};

export default function HomeLayout() {
  return (
    <ClassTabProvider>
      <ClassLayout />
    </ClassTabProvider>
  );
}
