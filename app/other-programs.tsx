import React, { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { Icon } from "@/components/ui/icon";
import { Input, InputField, InputIcon, InputSlot } from "@/components/ui/input";
import { ScrollView } from "react-native";
import { router } from "expo-router";
import { ArrowLeft, Search, SlidersHorizontal } from "lucide-react-native";
import { usePrograms } from "@/data/screens/common/queries/usePrograms";
import { ProgramResponse } from "@/data/screens/common/types";

const ProgramCard: React.FC<ProgramResponse & { isMore?: boolean }> = ({
  title,
  isMore = false,
  url,
}) => {
  return (
    <Pressable
      onPress={() => router.push({ pathname: "/webview", params: { url } })}
      className="bg-white rounded-2xl p-4 flex-1 min-h-[120px] justify-center items-center border border-background-100 active:bg-background-50"
    >
      <VStack className="items-center justify-center flex-1" space="sm">
        <VStack className="bg-primary-50 rounded-full p-3 items-center justify-center">
          <Icon
            size="lg"
            className={isMore ? "text-primary-500" : "text-primary-600"}
          />
        </VStack>
        <VStack className="items-center" space="xs">
          <Text className="text-sm font-dm-sans-medium text-typography-900 text-center">
            {title}
          </Text>
        </VStack>
      </VStack>
    </Pressable>
  );
};

export const OtherPrograms = () => {
  const [searchTerm, setSearchTerm] = useState("");

  const { data } = usePrograms();

  const handleBack = () => {
    router.back();
  };

  const handleSearch = (text: string) => {
    setSearchTerm(text);
  };

  return (
    <SafeAreaView className="flex-1 bg-background-50">
      <VStack className="flex-1">
        {/* Header */}
        <HStack className="px-4 py-4 bg-white items-center justify-between">
          <HStack className="items-center" space="md">
            <Pressable onPress={handleBack} className="p-1">
              <Icon as={ArrowLeft} size="lg" className="text-typography-900" />
            </Pressable>
            <Text className="text-lg font-dm-sans-bold text-typography-900">
              Other programs
            </Text>
          </HStack>
        </HStack>

        {/* Search */}
        <VStack className="px-4 pb-4 bg-white">
          <Input
            variant="outline"
            className="bg-background-50 rounded-xl"
            size="lg"
          >
            <InputSlot>
              <InputIcon as={Search} className="text-typography-400 ml-3" />
            </InputSlot>
            <InputField
              placeholder="Search"
              className="placeholder:text-typography-400"
              onChangeText={handleSearch}
              value={searchTerm}
            />
            <InputSlot>
              <Pressable className="p-2 bg-background-100 rounded-lg mr-2 border border-background-200">
                <Icon
                  as={SlidersHorizontal}
                  size="sm"
                  className="text-typography-600"
                />
              </Pressable>
            </InputSlot>
          </Input>
        </VStack>

        <ScrollView
          className="flex-1"
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingVertical: 16 }}
        >
          <VStack className="px-4 mb-6" space="md">
            {data &&
              Array.from({ length: Math.ceil(data.length / 2) }, (_, i) =>
                data.slice(i * 2, i * 2 + 2)
              ).map((group, index) => (
                <HStack key={index} space="md">
                  {group.map((program) => (
                    <ProgramCard key={program.id} {...program} isMore />
                  ))}
                </HStack>
              ))}
          </VStack>
        </ScrollView>
      </VStack>
    </SafeAreaView>
  );
};

export default OtherPrograms;
