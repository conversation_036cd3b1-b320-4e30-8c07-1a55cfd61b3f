import { api } from "@/lib/api";
import { EventsApiResponse } from "../screens/events/types";

export const fetchEventsByDate = async ({ date }: { date: string }) => {
  try {
    const urlParams = new URLSearchParams({
      date,
    }).toString();

    const response = await api
      .get<EventsApiResponse>(`events/reservations/list?${urlParams}`)
      .json();

    return response?.events || [];
  } catch (err) {
    throw new Error("Could not fetch events", err as Error);
  }
};
