import { api } from "@/lib/api";
import {
  ActivitiesApiResponse,
  ActivitiesQueryParams,
  ActivityReservationRequest,
  ActivityReservationResponse,
  ActivityTimeSlot,
} from "../screens/activities/types";
import { mockActivities } from "../screens/activities/mock-data";

export const fetchActivitiesByDate = async (params: ActivitiesQueryParams) => {
  try {
    // For development, return mock data
    // TODO: Replace with actual API call when backend is ready
    await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate network delay

    let filteredActivities = [...mockActivities];

    // Apply filters
    if (params.facility_id && params.facility_id !== "") {
      filteredActivities = filteredActivities.filter(
        (activity) => activity.gym_id === parseInt(params.facility_id!)
      );
    }

    if (params.category) {
      filteredActivities = filteredActivities.filter(
        (activity) =>
          activity.type.toLowerCase() === params.category!.toLowerCase()
      );
    }

    if (params.status) {
      filteredActivities = filteredActivities.filter(
        (activity) =>
          activity.current_status.toLowerCase() === params.status!.toLowerCase()
      );
    }

    return filteredActivities;

    // Uncomment when API is ready:
    // const urlParams = new URLSearchParams();
    // if (params.date) urlParams.append("date", params.date);
    // if (params.facility_id) urlParams.append("facility_id", params.facility_id);
    // if (params.category) urlParams.append("category", params.category);
    // if (params.status) urlParams.append("status", params.status);
    // const response = await api
    //   .get<ActivitiesApiResponse>(`activities/list?${urlParams.toString()}`)
    //   .json();
    // return response?.activities || [];
  } catch (err) {
    throw new Error("Could not fetch activities");
  }
};

export const fetchActivityTimeSlots = async (
  equipmentId: number,
  date: string
) => {
  try {
    const response = await api
      .get<{
        success: boolean;
        time_slots: ActivityTimeSlot[];
      }>(`activities/${equipmentId}/time-slots?date=${date}`)
      .json();

    return response?.time_slots || [];
  } catch (err) {
    throw new Error("Could not fetch activity time slots");
  }
};

export const reserveActivity = async (data: ActivityReservationRequest) => {
  try {
    const response = await api
      .post<ActivityReservationResponse>("activities/reserve", {
        json: data,
      })
      .json();

    return response;
  } catch (err) {
    if (err instanceof Error) {
      throw err;
    }
    throw new Error(String(err));
  }
};

export const cancelActivityReservation = async (reservationId: number) => {
  try {
    const response = await api
      .post<{ success: boolean; message?: string }>(
        "activities/reservations/cancel",
        {
          json: {
            reservation_id: reservationId,
          },
        }
      )
      .json();

    return response;
  } catch (err) {
    if (err instanceof Error) {
      throw err;
    }
    throw new Error(String(err));
  }
};
