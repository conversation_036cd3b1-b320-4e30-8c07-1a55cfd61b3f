/* eslint-disable @typescript-eslint/no-unused-vars */
import { api } from "@/lib/api";
import { AppointmentType, CategoryType } from "../screens/appointments/types";

export const fetchAppointmentByCategory = async (
  params: Record<string, string | number>
) => {
  try {
    const urlParams = new URLSearchParams({
      id: params?.id as string,
      gym_id: params?.gym_id as string,
    }).toString();

    const response = await api
      .get<{
        data: AppointmentType[];
      }>(`pt/appointments?categories?${urlParams}`)
      .json();

    return response.data;
  } catch (err) {
    throw new Error("Could not fetch appointments");
  }
};

export const fetchAppointments = async ({ date }: { date: string }) => {
  try {
    const urlParams = new URLSearchParams({
      date,
    }).toString();

    const response = await api
      .get<{
        data: AppointmentType[];
      }>(`pt/appointments?${urlParams}`)
      .json();

    return response.data;
  } catch (err) {
    throw new Error("Could not fetch appointments");
  }
};

export const fetchCategoriesAppointments = async () => {
  try {
    const response = await api
      .get<{
        data: CategoryType[];
      }>(`pt/appointments/categories`)
      .json();

    return response.data;
  } catch (err) {
    throw new Error("Could not fetch appointments");
  }
};
