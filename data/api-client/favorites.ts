/* eslint-disable @typescript-eslint/no-unused-vars */
import { api } from "@/lib/api";

export interface ToggleFavoriteParams {
  type: "class" | "equipment";
  item_id: number;
}

export interface ToggleFavoriteResponse {
  success: boolean;
  message: string;
  is_favorite: boolean;
}

export const toggleFavorite = async (
  params: ToggleFavoriteParams
): Promise<ToggleFavoriteResponse> => {
  try {
    const response = await api
      .post<ToggleFavoriteResponse>("favorites/mine/toggle", {
        json: {
          type: params.type,
          item_id: Number(params.item_id),
        },
      })
      .json();

    return response;
  } catch (err) {
    throw new Error("Could not toggle favorite");
  }
};
