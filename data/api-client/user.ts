/* eslint-disable @typescript-eslint/no-unused-vars */
import { api } from "@/lib/api";
import { UserResponse } from "../screens/common/types";

export const fetchUserInfo = async (id: string) => {
  try {
    const response = await api
      .get<{ data: UserResponse }>(`users/${id}`)
      .json();
    return response.data;
  } catch (err) {
    throw new Error("Could not fetch appointments");
  }
};
