import {
  fetchAppointmentByCategory,
  fetchAppointments,
  fetchCategoriesAppointments,
} from "./appointment";
import { fetchClassesByOrgId } from "./classes";
import { fetchClientInfoByOrgId } from "./client-info";
import { fetchEventsByDate } from "./events";
import { toggleFavorite } from "./favorites";
import { fetchPrograms } from "./programs";
import { fetchTrainersByOrgId, fetchTrainersBySessionId } from "./trainer";
import { fetchUserInfo } from "./user";

export const apiClient = {
  getTrainersBySessionId: fetchTrainersBySessionId,
  getTrainersByOrgId: fetchTrainersByOrgId,
  getAppointmentsByCategory: fetchAppointmentByCategory,
  getAppointments: fetchAppointments,
  getCategoriesAppointments: fetchCategoriesAppointments,
  getClassesByOrgId: fetchClassesByOrgId,
  getEventsByDate: fetchEventsByDate,
  getPrograms: fetchPrograms,
  getUserInfo: fetchUserInfo,
  toggleFavorite: toggleFavorite,
  getClientInfoByOrgId: fetchClientInfoByOrgId,
};
