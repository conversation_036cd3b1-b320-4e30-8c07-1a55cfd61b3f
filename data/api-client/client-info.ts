import { api } from "@/lib/api";
import { ClientData } from "../screens/common/types";

export const fetchClientInfoByOrgId = async ({ orgId }: { orgId: string }) => {
  try {
    const urlParams = new URLSearchParams({
      uni_id: orgId,
    }).toString();

    const response = await api
      .get<{
        data: ClientData;
      }>(`client/info?${urlParams}`)
      .json();

    return response?.data;
  } catch (err) {
    throw new Error("Could not fetch events", err as Error);
  }
};
