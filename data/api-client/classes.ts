/* eslint-disable @typescript-eslint/no-unused-vars */
import { api } from "@/lib/api";
import { ClassDetailsResponse } from "../screens/classes/types";

export const fetchClassesByOrgId = async ({
  orgId,
  date,
  gym_id,
}: {
  orgId: string;
  date: string;
  gym_id?: string;
}) => {
  try {
    const urlParams = new URLSearchParams({
      university_id: orgId,
      date,
      gym_id: gym_id as string,
    }).toString();

    const response = await api
      .get<{
        classes: ClassDetailsResponse[];
      }>(
        `classes/reservations/list?${urlParams}&exclude_if_closed_or_cancelled=true&exclude_past_classes=true`
      )
      .json();

    return response?.classes;
  } catch (err) {
    throw new Error("Could not fetch classes");
  }
};
