import { DATE_FORMAT } from "@/constants/date-formats";
import { format, parse, parseISO } from "date-fns";

export const formatDate = (date?: string | Date | undefined | null) => {
  if (date && typeof date === "object" && date instanceof Date) {
    return format(parseISO(date.toISOString()), DATE_FORMAT.YEAR_MONTH_DAY);
  }

  const isoDate = date ? parseISO(date) : parseISO(new Date().toISOString());
  return format(isoDate, DATE_FORMAT.YEAR_MONTH_DAY);
};
// Predefined color palette for initials
const INITIAL_COLORS = [
  "#FF6B6B", // Red
  "#4ECDC4", // Teal
  "#45B7D1", // Blue
  "#96CEB4", // Green
  "#FFEAA7", // Yellow
  "#DDA0DD", // Plum
  "#98D8C8", // Mint
  "#F7DC6F", // Light Yellow
  "#BB8FCE", // Light Purple
  "#85C1E9", // Light Blue
  "#82E0AA", // Light Green
  "#F8C471", // Light Orange
  "#F1948A", // Light Red
  "#AED6F1", // Pale Blue
  "#A9DFBF", // Pale Green
];

export const getInitials = (name: string) => {
  const words = name.split(" ");
  // Only take the first two words
  const firstTwoWords = words.slice(0, 2);
  return firstTwoWords
    .map((word) => word.charAt(0))
    .join("")
    .toUpperCase();
};

export const getRandomColorForInitials = (name: string) => {
  // Use the name as a seed to ensure consistent color for the same name
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    const char = name.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  // Use absolute value and modulo to get a consistent index
  const colorIndex = Math.abs(hash) % INITIAL_COLORS.length;
  return INITIAL_COLORS[colorIndex];
};

export const formatTime = (time: string) => {
  if (!time) {
    return "-";
  }

  const splittedTime = time.split(" ");

  const timeSplitted = splittedTime[1] ?? splittedTime[0] ?? "";

  const parsedTime = parse(timeSplitted, "HH:mm:ss", new Date());

  // To allow no space between time and am/pm
  return format(parsedTime, "h:mma");
};

export const obtainDateFrame = (startTime: string, endTime: string) => {
  if (startTime?.length === 5) {
    startTime += ":00";
  }

  if (endTime?.length === 5) {
    endTime += ":00";
  }
  return `${formatTime(startTime)} - ${formatTime(endTime)}`;
};

export const formatHourMinsTime = (time: string) => {
  const parsedTime = parse(time, "HH:mm:ss", new Date());
  return format(parsedTime, "HH:MM");
};
