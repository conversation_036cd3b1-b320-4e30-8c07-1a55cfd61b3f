import { Specialty } from "../appointments/types";

interface SecondaryGym {
  id: number;
  name: string;
}

interface TrainingSession {
  id: number;
  name: string;
  gym_name: string;
}

interface TimeSlot {
  id: number;
  start_time: string;
  end_time: string;
  day_of_week: string;
  day_of_week_name: string;
  schedule_id: number;
}

interface ScheduleDetail {
  id: number;
  gym_id: number;
  gym_name: string;
  room_id: number;
  room_name: string;
  active_in_app: number;
  equipment_categories: unknown[]; // kept generic; refine if known
}

interface ScheduleBlock {
  schedule: ScheduleDetail;
  time_slots: TimeSlot[];
}

interface RoleDetails {
  id: number;
  role: string;
  access_level: number;
}

interface ClassInstructorPayroll {
  class_id: number;
  class_name: string;
  class_instructor_id: number;
  class_start_time: string;
  class_end_time: string;
  gym_name: string;
  instructor_first_name: string;
  instructor_last_name: string;
  instructor_email: string;
  pay_amount: number;
  payroll_instructor_id: number;
  is_instructor_for_class: boolean;
}

// --- MAIN USER TYPE ---
interface User {
  id: number;
  created: string;
  updated: string;
  university_id: number;
  gym_id: number;
  secondary_gym_ids: string;
  primary_role: number;
  account_status: number;
  username: string;
  first_name: string;
  last_name: string;
  profile_image: string | null;
  email: string;
  phone: string;
  sex: string;
  frequency: string | null;
  class: string | null;
  program_preferences: string | null;
  specialties: Specialty[];
  years_experience: number;
  bio: string | null;
  dob: string | null;
  barcode: string | null;
  ymca_id: string | null;
  foreign_id: string | null;
  is_paid: number;
  auto_suspended: number;
  is_trainer: number;
  has_subscription: number;
  deleted: number;
  creation_source: string | null;
  update_source: string | null;
  created_by: number | null;
  last_updated_by: number | null;
  commission_percentage: number;
  is_vaccinated: number;
  vaccination_expires: string | null;
  deleted_by: number | null;
  deleted_on: string | null;
  last_logged_in: string | null;
  active: number;
  gym_name: string;
  role: string;
  secondary_gyms: SecondaryGym[];
  memberships: string[];
  training_sessions: TrainingSession[];
  block_schedules: ScheduleBlock[]; // assuming similar shape, adjust if needed
  schedule: ScheduleBlock[];
  certifications: unknown[];
  availability_schedule: unknown[];
  roleDetails: RoleDetails;
  class_instructor_payroll: ClassInstructorPayroll[];
  family_members: string[];
}

export interface UserResponse {
  user: User;
  memberships: string[];
}

export interface ProgramResponse {
  id: number | string;
  university_id: number;
  gym_id: number;
  title: string;
  text: string | null;
  url: string | null;
  icon: string;
  all_locations: number;
  created: string;
  active: number;
  gym_name: string | null;
}

type Facility = {
  id: number;
  name: string;
};

type ClassCategory = {
  id: number;
  category_name: string;
  university_id: number;
};

type EquipmentType = {
  id: number;
  name: string;
};

type TrainerSpecialty = {
  id: number;
  name: string;
};

type PtCategory = {
  id: number;
  name: string;
};

type PtSession = {
  id: number;
  name: string;
  gym_id: number;
  category_id: number;
};

export type ClientData = {
  client_name: string;
  client_logo: string;
  client_background: string;
  client_disclaimers: {
    pdf_header: string;
    pdf_footer: string;
  };
  facilities: Facility[];
  class_categories: ClassCategory[];
  equipment_types: EquipmentType[];
  trainer_specialties: TrainerSpecialty[];
  pt_categories: PtCategory[];
  pt_sessions: PtSession[];
  timezone: string;
  app_urls: {
    ios: string;
    android: string;
  };
  legacy_app_configs: Record<string, string>;
  legacy_app_modules: string[];
  configs: {
    embed: {
      button_color: string;
      accent_color: string;
      sgt_color: string;
      font_style: string;
      display_page_header_background_image: string;
      display_page_clock: boolean;
      display_page_logo: boolean;
    };
    class_checkin: {
      checkin_user_search_options: string[];
      mins_before_class_to_show_reservation: string;
      mins_after_class_to_show_reservation: string;
      logout_timeout_secs: string;
      camera_position: string;
      logo_url: string;
      background_image_url: string;
    };
  };
};
