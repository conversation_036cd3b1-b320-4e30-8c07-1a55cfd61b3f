import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "@/data/api-client";
import { ToastManager } from "@/components/shared/toast/ToastManager";

export const useFavoriteMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: apiClient.toggleFavorite,
    onSuccess: (data) => ToastManager.show(data.message, "success"),
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["classes"] });
      queryClient.invalidateQueries({ queryKey: ["events"] });
    },
  });
};
