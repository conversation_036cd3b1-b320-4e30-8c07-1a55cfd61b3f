import { ClassDetailsResponse } from "./types";

export const obtainSpotsAvailable = (
  type: "Virtual" | "Live" | "Virtual & Live",
  classData?: ClassDetailsResponse
) => {
  switch (type) {
    case "Virtual":
      return classData?.virtual_spots_available;
    case "Live":
      return classData?.spots_available;
    case "Virtual & Live":
      return (
        (classData?.virtual_spots_available ?? 0) +
        (classData?.spots_available ?? 0)
      );
    default:
      return 0;
  }
};

export const obtainStatus = (type: "Virtual" | "Live" | "Virtual & Live") => {
  switch (type) {
    case "Virtual":
      return {
        classes: "bg-[#FCF4E7]",
        text: "Virtual",
      };
    case "Live":
      return {
        classes: "bg-[#FCF4E7]",
        text: "Paid",
      };
    case "Virtual & Live":
      return {
        classes: "bg-[#FCF4E7]",
        text: "Paid",
      };
    default:
      return {
        classes: "",
        text: "",
      };
  }
};
