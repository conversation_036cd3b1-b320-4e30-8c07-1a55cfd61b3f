import { formatDate } from "@/data/common/common.utils";
import { api } from "@/lib/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

interface ReservationEventData {
  class_id: number;
  date: string;
  is_virtual: boolean;
  type?: string;
}

export const reserveAction = async (data: ReservationEventData) => {
  try {
    const result = await api
      .post("reserve", {
        json: {
          ...data,
          date: formatDate(data.date),
          type: "class",
        },
      })
      .json();

    return result;
  } catch (err) {
    // If err is already an Error object with a message, use it directly
    if (err instanceof Error) {
      throw err;
    }
    // Otherwise, create a new Error with the string representation
    throw new Error(String(err));
  }
};

export const useReserveClass = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: reserveAction,
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: ["classes"],
      });

      if (onSuccess) {
        onSuccess();
      }
    },
    onSettled: () => {
      onSuccess?.();
    },
  });
};
