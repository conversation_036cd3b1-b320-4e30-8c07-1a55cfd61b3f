import { useQuery } from "@tanstack/react-query";
import { EventsQueryParams } from "../types";
import { apiClient } from "@/data/api-client";

export const useEventsQuery = (params?: EventsQueryParams) => {
  return useQuery({
    queryKey: ["events", params?.date],
    queryFn: () => {
      if (!params?.date) {
        throw new Error("Date parameter is required");
      }
      return apiClient.getEventsByDate({ date: params.date });
    },
    enabled: !!params?.date,
  });
};
