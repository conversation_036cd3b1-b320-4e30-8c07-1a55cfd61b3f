export type EventStatus =
  | "paid"
  | "cancelled"
  | "event_cancelled"
  | "event_full"
  | "join_waitlist";

export interface EventReservation {
  id: number;
  class_id: number;
  start_time: string;
  end_time: string;
  checkin: number;
}

export interface EventResponse {
  id: number;
  created: string;
  updated: string;
  university_id: number;
  gym_id: number;
  instructor_id: number;
  room_id: number;
  start_day: string;
  end_day: string;
  allow_reservation_date: string;
  day: string;
  start_time: string;
  end_time: string;
  name: string;
  description: string;
  advance_time: number;
  advance_days: string;
  recurring: number;
  days: string;
  spots: number;
  walkin_spots: number;
  member_spots: number;
  virtual_spots: number;
  is_event: number;
  is_paid: number;
  is_recommend: number;
  file_id: number;
  allow_feedback: number;
  show_feedback: number;
  price: string;
  member_price: string;
  foreign_id: string | null;
  tags: string;
  show_in_app: number;
  active: number;
  deleted: number;
  class_category_id: number;
  class_type: string;
  class_url_link: string | null;
  allow_waitlist: number;
  waitlist_spots: number;
  waitlist_time_lt: number;
  res_cancel_time_lt: number;
  res_before_start_lt: number;
  is_sgt: number;
  image_url: string | null;
  customfield: string | null;
  gym_name: string;
  room_name: string;
  reservations_count: number;
  day_of_week: string;
  date: string;
  reservations: EventReservation[];
  reservation_count: number;
  is_favourite?: boolean;
}

export interface EventsApiResponse {
  success: boolean;
  events: EventResponse[];
}

export interface EventsQueryParams {
  date?: string;
  month?: string;
  category?: string;
}
