import { ActivityEquipment } from "./types";

export const mockActivities: ActivityEquipment[] = [
  {
    id: 1,
    name: "Treadmill",
    description: "High-quality treadmill for cardio workouts",
    image_url: undefined,
    facility_id: 1,
    facility_name: "<PERSON> & catz family",
    category: "Cardio",
    status: "reserved",
    next_available_time: "02:00PM",
    duration_options: [30, 60, 90, 120],
    advance_booking_hours: 24,
    max_booking_duration: 120,
    is_favorite: false,
  },
  {
    id: 2,
    name: "Treadmill",
    description: "High-quality treadmill for cardio workouts",
    image_url: undefined,
    facility_id: 1,
    facility_name: "<PERSON> & catz family",
    category: "Cardio",
    status: "available",
    duration_options: [30, 60, 90, 120],
    advance_booking_hours: 24,
    max_booking_duration: 120,
    is_favorite: true,
  },
  {
    id: 3,
    name: "Stationary Bike",
    description: "Professional stationary bike",
    image_url: undefined,
    facility_id: 1,
    facility_name: "<PERSON> & catz family",
    category: "Cardio",
    status: "full",
    duration_options: [30, 60, 90],
    advance_booking_hours: 12,
    max_booking_duration: 90,
    is_favorite: false,
  },
  {
    id: 4,
    name: "Free Weights",
    description: "Complete set of free weights",
    image_url: undefined,
    facility_id: 1,
    facility_name: "Collins & catz family",
    category: "Strength",
    status: "available",
    next_available_time: "2:00PM",
    duration_options: [30, 60, 90, 120],
    advance_booking_hours: 24,
    max_booking_duration: 120,
    is_favorite: true,
  },
  {
    id: 5,
    name: "Yoga Mat",
    description: "Premium yoga mat for stretching and yoga",
    image_url: undefined,
    facility_id: 1,
    facility_name: "Collins & catz family",
    category: "Flexibility",
    status: "full",
    duration_options: [30, 60, 90],
    advance_booking_hours: 12,
    max_booking_duration: 90,
    is_favorite: false,
  },
  {
    id: 6,
    name: "Rowing Machine",
    description: "Professional rowing machine for full-body workout",
    image_url: undefined,
    facility_id: 1,
    facility_name: "Collins & catz family",
    category: "Cardio",
    status: "available",
    next_available_time: "2:00PM",
    duration_options: [30, 60, 90],
    advance_booking_hours: 24,
    max_booking_duration: 90,
    is_favorite: false,
  },
  {
    id: 7,
    name: "Resistance Bands",
    description: "Set of resistance bands for strength training",
    image_url: undefined,
    facility_id: 1,
    facility_name: "Collins & catz family",
    category: "Strength",
    status: "available",
    next_available_time: "2:00PM",
    duration_options: [30, 60, 90],
    advance_booking_hours: 12,
    max_booking_duration: 90,
    is_favorite: false,
  },
  {
    id: 8,
    name: "Treadmill",
    description: "High-quality treadmill for cardio workouts",
    image_url: undefined,
    facility_id: 1,
    facility_name: "Collins & catz family",
    category: "Cardio",
    status: "available",
    next_available_time: "2:00PM",
    duration_options: [30, 60, 90, 120],
    advance_booking_hours: 24,
    max_booking_duration: 120,
    is_favorite: false,
  },
  {
    id: 9,
    name: "Elliptical Trainer",
    description: "Low-impact elliptical trainer",
    image_url: undefined,
    facility_id: 1,
    facility_name: "Collins & catz family",
    category: "Cardio",
    status: "available",
    next_available_time: "2:00PM",
    duration_options: [30, 60, 90],
    advance_booking_hours: 24,
    max_booking_duration: 90,
    is_favorite: false,
  },
];
