export interface ActivityEquipment {
  id: number;
  name: string;
  description?: string;
  image_url?: string;
  facility_id: number;
  facility_name: string;
  category: string;
  status: 'available' | 'reserved' | 'maintenance' | 'full';
  next_available_time?: string;
  duration_options: number[]; // in minutes
  advance_booking_hours: number;
  max_booking_duration: number; // in minutes
  is_favorite?: boolean;
}

export interface ActivityReservation {
  id: number;
  equipment_id: number;
  user_id: number;
  start_time: string;
  end_time: string;
  duration: number; // in minutes
  status: 'active' | 'cancelled' | 'completed';
  created_at: string;
}

export interface ActivityTimeSlot {
  start_time: string;
  end_time: string;
  available: boolean;
  equipment_id: number;
}

export interface ActivitiesApiResponse {
  success: boolean;
  activities: ActivityEquipment[];
}

export interface ActivityReservationRequest {
  equipment_id: number;
  start_time: string;
  duration: number; // in minutes
  facility_id?: number;
}

export interface ActivityReservationResponse {
  success: boolean;
  reservation: ActivityReservation;
  message?: string;
}

export interface ActivitiesQueryParams {
  date?: string;
  facility_id?: string;
  category?: string;
  status?: string;
}
