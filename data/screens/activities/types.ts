export interface TimeSlot {
  equ_slot_id: number;
  start_time: string;
  end_time: string;
  day_of_week: string;
}

export interface AvailableTimeSlot {
  time_value: string;
  time_label: string;
  full_date_time: string;
  minimum_end_time: string;
  full_minimum_end_time: string;
  reservations_at_slot: number;
  remarks: string;
  allow_reservations: boolean;
}

export interface Duration {
  value: number;
  label: string;
}

export interface AttendingPerson {
  value: number;
  label: string;
}

export interface ActivityEquipment {
  id: number;
  gym_id: number;
  room_id: number;
  total_slots: number;
  name: string;
  description: string;
  use_room_times: number;
  duration_time_interval: number;
  max_reservations: number;
  min_time_lt: string;
  mac_time_lt: string;
  max_attending_persons: number;
  type_id: number;
  start_date: string | null;
  end_date: string | null;
  image_url: string | null;
  type: string;
  gym_name: string;
  room_name: string;
  day_of_week: string;
  uniq: string;
  facility_closed: boolean;
  is_user_favorite: boolean;
  time_slots: TimeSlot[];
  available_time_slots: AvailableTimeSlot[];
  next_available_time_slot: AvailableTimeSlot | null;
  durations: Duration[];
  attending_persons: AttendingPerson[];
  status: string | null;
  current_status: string;
  is_favourite: boolean;
}

export interface ActivityReservation {
  id: number;
  equipment_id: number;
  user_id: number;
  start_time: string;
  end_time: string;
  duration: number; // in minutes
  status: "active" | "cancelled" | "completed";
  created_at: string;
}

export interface ActivityTimeSlot {
  start_time: string;
  end_time: string;
  available: boolean;
  equipment_id: number;
}

export interface ActivitiesApiResponse {
  success: boolean;
  activities: ActivityEquipment[];
}

export interface ActivityReservationRequest {
  equipment_id: number;
  start_time: string;
  duration: number; // in minutes
  facility_id?: number;
}

export interface ActivityReservationResponse {
  success: boolean;
  reservation: ActivityReservation;
  message?: string;
}

export interface ActivitiesQueryParams {
  date?: string;
  facility_id?: string;
  category?: string;
  status?: string;
}
