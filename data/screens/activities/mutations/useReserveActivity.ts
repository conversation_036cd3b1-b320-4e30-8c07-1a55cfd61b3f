import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "@/data/api-client";
import { ActivityReservationRequest } from "../types";

export const useReserveActivity = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ActivityReservationRequest) => 
      apiClient.reserveActivity(data),
    onSuccess: (data, variables) => {
      // Invalidate and refetch activities queries
      queryClient.invalidateQueries({ queryKey: ["activities"] });
      queryClient.invalidateQueries({ 
        queryKey: ["activity-time-slots", variables.equipment_id] 
      });
    },
  });
};

export const useCancelActivityReservation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (reservationId: number) => 
      apiClient.cancelActivityReservation(reservationId),
    onSuccess: () => {
      // Invalidate and refetch activities queries
      queryClient.invalidateQueries({ queryKey: ["activities"] });
      queryClient.invalidateQueries({ queryKey: ["activity-time-slots"] });
    },
  });
};
