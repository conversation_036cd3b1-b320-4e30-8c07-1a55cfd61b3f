import { useQuery } from "@tanstack/react-query";
import { ActivitiesQueryParams } from "../types";
import { apiClient } from "@/data/api-client";

export const useActivitiesQuery = (params?: ActivitiesQueryParams) => {
  return useQuery({
    queryKey: ["activities", params?.date, params?.facility_id, params?.category, params?.status],
    queryFn: () => {
      if (!params?.date) {
        throw new Error("Date parameter is required");
      }
      return apiClient.getActivitiesByDate(params);
    },
    enabled: !!params?.date,
  });
};

export const useActivityTimeSlotsQuery = (equipmentId: number, date: string) => {
  return useQuery({
    queryKey: ["activity-time-slots", equipmentId, date],
    queryFn: () => apiClient.getActivityTimeSlots(equipmentId, date),
    enabled: !!equipmentId && !!date,
  });
};
