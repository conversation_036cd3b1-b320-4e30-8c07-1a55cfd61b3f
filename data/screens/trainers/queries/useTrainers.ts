import { useSession } from "@/modules/login/auth-provider";
import { queryOptions, useQuery } from "@tanstack/react-query";

import { apiClient } from "@/data/api-client";

const getTrainersBySession = (id: string) =>
  queryOptions({
    queryKey: ["trainers", id],
    queryFn: () => apiClient.getTrainersBySessionId(id),
    enabled: !!id,
  });

const getAllTrainers = (orgId: string) =>
  queryOptions({
    queryKey: ["trainers", orgId],
    queryFn: () => apiClient.getTrainersByOrgId({ orgId }),
    enabled: !!orgId,
  });

export const useTrainers = (sessionId: string) => {
  const { data: sessionData } = useSession();

  return useQuery(
    sessionId
      ? getTrainersBySession(sessionId)
      : getAllTrainers(sessionData?.university_id as string)
  );
};
