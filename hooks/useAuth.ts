import { useCallback } from 'react';
import { router } from 'expo-router';
import { useAuthStore } from '@/stores/auth-store';
import { useLoginQuery } from '@/modules/login/hooks/useLoginQuery';
import { useBiometricAuth } from '@/modules/login/hooks/useBiometricAuth';
import { needsToAcceptTerms, clearTermsAcceptance } from '@/modules/terms/terms-storage';

export const useAuth = () => {
  const {
    user,
    isAuthenticated,
    isLoading,
    isError,
    error,
    showTerms,
    biometricState,
    setUser,
    setLoading,
    setError,
    setShowTerms,
    setBiometricState,
    signOut: storeSignOut,
  } = useAuthStore();

  const { mutate: loginMutation } = useLoginQuery();
  const {
    state: biometricAuthState,
    authenticate: authenticateBiometric,
    enableBiometric: enableBiometricAuth,
    disableBiometric: disableBiometricAuth,
    checkBiometricStatus: checkBiometricAuthStatus,
  } = useBiometricAuth();

  const signIn = useCallback(
    async ({ email, password, orgId }: { email: string; password: string; orgId?: string }) => {
      setLoading(true);
      setError(null);

      try {
        loginMutation(
          { email, password, orgId },
          {
            onSuccess: async (data) => {
              setUser(data);
              
              // Check if user needs to accept terms
              const needsAcceptance = await needsToAcceptTerms();
              if (needsAcceptance) {
                setShowTerms(true);
              } else {
                router.replace('/(tabs)/(home)');
              }
            },
            onError: (error) => {
              setError(error as Error);
            },
            onSettled: () => {
              setLoading(false);
            },
          }
        );
      } catch (error) {
        setError(error as Error);
        setLoading(false);
      }
    },
    [loginMutation, setUser, setLoading, setError, setShowTerms]
  );

  const signInWithBiometric = useCallback(async (): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const success = await authenticateBiometric();
      if (success) {
        router.replace('/(tabs)/(home)');
      }
      return success;
    } catch (error) {
      setError(error as Error);
      return false;
    } finally {
      setLoading(false);
    }
  }, [authenticateBiometric, setLoading, setError]);

  const enableBiometric = useCallback(
    async (email: string, password: string): Promise<boolean> => {
      try {
        const success = await enableBiometricAuth(email, password);
        if (success) {
          setBiometricState({ isEnabled: true });
        }
        return success;
      } catch (error) {
        setError(error as Error);
        return false;
      }
    },
    [enableBiometricAuth, setBiometricState, setError]
  );

  const disableBiometric = useCallback(async (): Promise<boolean> => {
    try {
      const success = await disableBiometricAuth();
      if (success) {
        setBiometricState({ isEnabled: false });
      }
      return success;
    } catch (error) {
      setError(error as Error);
      return false;
    }
  }, [disableBiometricAuth, setBiometricState, setError]);

  const checkBiometricStatus = useCallback(async (): Promise<void> => {
    try {
      await checkBiometricAuthStatus();
      setBiometricState(biometricAuthState);
    } catch (error) {
      console.error('Failed to check biometric status:', error);
    }
  }, [checkBiometricAuthStatus, setBiometricState, biometricAuthState]);

  const signOut = useCallback(async () => {
    try {
      await clearTermsAcceptance();
      storeSignOut();
      router.replace('/(auth)/sign-in');
    } catch (error) {
      console.error('Sign out error:', error);
    }
  }, [storeSignOut]);

  const onTermsAccepted = useCallback(() => {
    setShowTerms(false);
    router.replace('/(tabs)/(home)');
  }, [setShowTerms]);

  return {
    user,
    isAuthenticated,
    isLoading,
    isError,
    error,
    showTerms,
    biometricState,
    signIn,
    signInWithBiometric,
    enableBiometric,
    disableBiometric,
    checkBiometricStatus,
    signOut,
    onTermsAccepted,
  };
};
