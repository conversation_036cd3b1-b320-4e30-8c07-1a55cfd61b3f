import { useCallback, useMemo, useState } from "react";
import { matchSorter } from "match-sorter";
import { useEventsQuery } from "@/data/screens/events/queries/useEventsQuery";
import { format } from "date-fns";

export const useEvents = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDate, setSelectedDate] = useState(new Date());

  const [facility, setFacility] = useState("");

  // Fetch events data
  const {
    data: eventsData = [],
    isLoading,
    error,
    refetch,
    isRefetching,
  } = useEventsQuery({
    date: format(selectedDate, "yyyy-MM"),
  });

  const filteredData = useMemo(() => {
    let filtered = eventsData;

    if (searchTerm) {
      filtered = matchSorter(filtered, searchTerm, {
        keys: ["name", "gym_name", "room_name", "description"],
      });
    }

    return filtered;
  }, [eventsData, searchTerm]);

  const handleDateChange = useCallback((date: Date) => {
    setSelectedDate(date);
  }, []);

  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
  }, []);

  const clearSearch = useCallback(() => {
    setSearchTerm("");
  }, []);

  const clearFilters = useCallback(() => {
    setSearchTerm("");
  }, []);

  return {
    events: filteredData,

    isLoading,
    isRefetching,
    error,

    selectedDate,
    searchTerm,

    handleDateChange,
    handleSearch,
    clearSearch,
    clearFilters,
    refetch,
    setSearchTerm,

    facility,
    setFacility,
  };
};
