/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useContext, useCallback } from "react";
import { Dimensions } from "react-native";
import { ClassTabContext } from "@/contexts/class-screen-context";

export const useChildVisibility = () => {
  const { scrollViewRef, childRefs, setChildRefs, selectedTabIndex }: any =
    useContext(ClassTabContext);

  const checkChildVisibility = useCallback(() => {
    if (childRefs.length !== 0) {
      let flag = false;
      const updatedChildRefs = childRefs.map((childRef: any) => {
        if (childRef.ref.current && scrollViewRef.current) {
          childRef.ref.current.measureLayout(
            scrollViewRef.current,
            (x: any, y: any, width: any, height: any) => {
              const windowHeight = Dimensions.get("window").height;

              const scrollY = scrollViewRef.current?._scrollY || 0;
              const isVisible =
                y >= scrollY && y <= scrollY + windowHeight - height;

              if (childRef.isVisible === false && isVisible) {
                childRef.isVisible = isVisible;
                flag = true;
              }
            }
          );
        }
        return childRef;
      });
      if (flag) {
        setChildRefs(updatedChildRefs);
      }
    }
  }, [childRefs, scrollViewRef, setChildRefs]);

  const handleScroll = (e: any) => {
    if (scrollViewRef.current) {
      scrollViewRef.current._scrollY = e.nativeEvent.contentOffset.y;
      checkChildVisibility();
    }
  };

  useEffect(() => {
    if (selectedTabIndex === 0) {
      checkChildVisibility();
    }
  }, [selectedTabIndex, checkChildVisibility]);

  return { handleScroll };
};
