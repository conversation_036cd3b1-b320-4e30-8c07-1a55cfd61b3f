import { useState, useMemo } from "react";
import { useActivitiesQuery } from "@/data/screens/activities/queries/useActivitiesQuery";
import { formatDate } from "@/data/common/common.utils";
import { matchSorter } from "match-sorter";

export const useActivities = () => {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [searchTerm, setSearchTerm] = useState("");
  const [facility, setFacility] = useState("");

  const formattedDate = formatDate(selectedDate);

  const { data: activities, isLoading, isRefetching, refetch } = useActivitiesQuery({
    date: formattedDate,
    facility_id: facility,
  });

  const filteredActivities = useMemo(() => {
    if (!activities) return [];
    
    let filtered = activities;
    
    if (searchTerm) {
      filtered = matchSorter(filtered, searchTerm, {
        keys: ["name", "facility_name", "category"],
      });
    }
    
    return filtered;
  }, [activities, searchTerm]);

  const handleDateChange = (date: Date) => {
    setSelectedDate(date);
  };

  const clearSearch = () => {
    setSearchTerm("");
  };

  return {
    activities: filteredActivities,
    isLoading,
    isRefetching,
    selectedDate,
    searchTerm,
    facility,
    handleDateChange,
    setSearchTerm,
    setFacility,
    clearSearch,
    refetch,
  };
};
